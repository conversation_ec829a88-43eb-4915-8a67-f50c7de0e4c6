# 🚀 Guía Completa de Despliegue - germayori.com

## ✅ Estado Actual - TODO LISTO
- ✅ **Dominio configurado**: https://germayori.com
- ✅ **Base de datos MongoDB**: Conectada y funcionando
- ✅ **Canal de señales OpenAI**: Configurado y operativo
- ✅ **Aplicación construida**: Lista para producción
- ✅ **Variables de entorno**: Configuradas correctamente

## 📦 Archivos Listos para Subir

Tu aplicación está completamente lista. Los archivos importantes son:
- ✅ Carpeta `.next/` (aplicación construida)
- ✅ Carpeta `public/` (archivos estáticos)
- ✅ Carpeta `src/` (código fuente)
- ✅ `package.json` (dependencias)
- ✅ `.env.production` (variables de entorno)
- ✅ `next.config.js` (configuración)

## 🌐 Opciones de Despliegue

### **OPCIÓN 1: Vercel (RECOMENDADO - MÁS FÁCIL)**

#### Paso 1: Crear cuenta en Vercel
1. Ve a [vercel.com](https://vercel.com)
2. Regístrate con GitHub, GitLab o email

#### Paso 2: Subir tu proyecto
```bash
# Instalar Vercel CLI
npm i -g vercel

# En tu carpeta del proyecto
vercel

# Seguir las instrucciones:
# - Set up and deploy? Y
# - Which scope? (tu cuenta)
# - Link to existing project? N
# - Project name: germayori
# - Directory: ./
# - Override settings? N
```

#### Paso 3: Configurar variables de entorno en Vercel
1. Ve a tu proyecto en vercel.com
2. Settings → Environment Variables
3. Agrega estas variables:
```
MONGODB_URI=mongodb+srv://jhon:<EMAIL>/?retryWrites=true&w=majority&appName=LA-LEGENDARIA-GERMAYORI
OPENAI_API_KEY=********************************************************************************************************************************************************************
NEXT_PUBLIC_BASE_URL=https://germayori.com
NEXT_PUBLIC_API_URL=https://germayori.com/api
JWT_SECRET=clave_super_secreta_para_produccion_2024_muy_larga_y_segura
SESSION_SECRET=otra_clave_super_secreta_para_sesiones_produccion_2024
NODE_ENV=production
```

#### Paso 4: Configurar dominio personalizado
1. En Vercel → Settings → Domains
2. Agregar: `germayori.com` y `www.germayori.com`
3. Configurar DNS en tu proveedor de dominio:
   - Tipo A: `@` → `***********`
   - Tipo CNAME: `www` → `cname.vercel-dns.com`

---

### **OPCIÓN 2: Servidor Propio (VPS/Hosting)**

#### Paso 1: Preparar servidor
```bash
# Conectar a tu servidor
ssh <EMAIL>

# Instalar Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Instalar PM2 para gestión de procesos
npm install -g pm2
```

#### Paso 2: Subir archivos
```bash
# Desde tu computadora, comprimir proyecto
tar -czf germayori.tar.gz .

# Subir al servidor
scp germayori.tar.gz usuario@tu-servidor:/var/www/

# En el servidor, extraer
cd /var/www/
tar -xzf germayori.tar.gz
```

#### Paso 3: Configurar en servidor
```bash
# Instalar dependencias
npm install --production

# Copiar variables de entorno
cp .env.production .env

# Iniciar con PM2
pm2 start npm --name "germayori" -- start
pm2 save
pm2 startup
```

#### Paso 4: Configurar Nginx
```nginx
# /etc/nginx/sites-available/germayori.com
server {
    listen 80;
    server_name germayori.com www.germayori.com;
    
    location / {
        proxy_pass http://localhost:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# Activar sitio
sudo ln -s /etc/nginx/sites-available/germayori.com /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

# Instalar SSL con Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d germayori.com -d www.germayori.com
```

---

## 🔧 Configuración DNS

Para que **germayori.com** funcione, configura estos registros DNS:

### Si usas Vercel:
```
Tipo A: @ → ***********
Tipo CNAME: www → cname.vercel-dns.com
```

### Si usas servidor propio:
```
Tipo A: @ → [IP de tu servidor]
Tipo CNAME: www → germayori.com
```

## 🧪 Probar Funcionamiento

Una vez desplegado, verifica:
1. **Página principal**: https://germayori.com
2. **Registro de usuarios**: Formulario en la página principal
3. **Login**: https://germayori.com/login
4. **Dashboard**: https://germayori.com/dashboard
5. **Canal de señales**: Funciona dentro del dashboard
6. **Base de datos**: Los usuarios se guardan correctamente

## 📞 Soporte Post-Despliegue

### Comandos útiles para servidor propio:
```bash
# Ver logs
pm2 logs germayori

# Reiniciar aplicación
pm2 restart germayori

# Ver estado
pm2 status

# Actualizar aplicación
git pull
npm run build
pm2 restart germayori
```

### Para Vercel:
- Los cambios se despliegan automáticamente al hacer push a GitHub
- Ver logs en el dashboard de Vercel

## 🎉 ¡Tu aplicación estará lista!

Con cualquiera de estas opciones, tu aplicación Germayori estará funcionando en https://germayori.com con:
- ✅ Registro y login de usuarios
- ✅ Base de datos MongoDB funcionando
- ✅ Canal de señales con OpenAI
- ✅ Dashboard completo
- ✅ Sistema de pagos
- ✅ Todas las funcionalidades

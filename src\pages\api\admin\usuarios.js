import { connectToDatabase } from '../../../lib/mongodb';

export default async function handler(req, res) {
  if (req.method === 'GET') {
    return await obtenerUsuarios(req, res);
  } else if (req.method === 'PUT') {
    return await actualizarUsuario(req, res);
  } else {
    return res.status(405).json({ success: false, message: 'Método no permitido' });
  }
}

// Obtener todos los usuarios (solo para administradores)
async function obtenerUsuarios(req, res) {
  try {
    // Conectar a MongoDB
    const { db } = await connectToDatabase();
    const collection = db.collection('usuarios');

    // Obtener todos los usuarios (excluyendo contraseñas)
    const usuarios = await collection.find({}, {
      projection: {
        password: 0 // Excluir contraseñas por seguridad
      }
    }).toArray();

    return res.status(200).json({
      success: true,
      usuarios: usuarios
    });

  } catch (error) {
    console.error('❌ Error obteniendo usuarios:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Error interno del servidor',
      error: error.message 
    });
  }
}

// Actualizar usuario (activar/desactivar, extender suscripción)
async function actualizarUsuario(req, res) {
  try {
    const { usuarioId, estado, extenderDias } = req.body;

    if (!usuarioId) {
      return res.status(400).json({
        success: false,
        message: 'ID de usuario requerido'
      });
    }

    // Conectar a MongoDB
    const { db } = await connectToDatabase();
    const collection = db.collection('usuarios');

    let updateData = {
      fechaActualizacion: new Date().toISOString()
    };

    // Si se especifica un nuevo estado
    if (estado) {
      updateData.estado = estado;
    }

    // Si se especifican días para extender
    if (extenderDias) {
      // Obtener usuario actual para calcular nueva fecha
      const usuario = await collection.findOne({ _id: usuarioId });
      if (!usuario) {
        return res.status(404).json({
          success: false,
          message: 'Usuario no encontrado'
        });
      }

      const fechaExpiracionActual = new Date(usuario.fechaExpiracion);
      const nuevaFechaExpiracion = new Date(fechaExpiracionActual);
      nuevaFechaExpiracion.setDate(fechaExpiracionActual.getDate() + parseInt(extenderDias));
      
      updateData.fechaExpiracion = nuevaFechaExpiracion.toISOString();
    }

    // Actualizar usuario
    const resultado = await collection.updateOne(
      { _id: usuarioId },
      { $set: updateData }
    );

    if (resultado.modifiedCount > 0) {
      return res.status(200).json({
        success: true,
        message: 'Usuario actualizado exitosamente'
      });
    } else {
      return res.status(404).json({
        success: false,
        message: 'Usuario no encontrado o sin cambios'
      });
    }

  } catch (error) {
    console.error('❌ Error actualizando usuario:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Error interno del servidor',
      error: error.message 
    });
  }
}

import { connectToDatabase } from '../../../lib/mongodb';
import { ObjectId } from 'mongodb';
import formidable from 'formidable';
import fs from 'fs';
import path from 'path';

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Método no permitido' });
  }

  try {
    // Crear directorio si no existe
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'perfiles');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    const form = formidable({
      uploadDir: uploadDir,
      keepExtensions: true,
      maxFileSize: 5 * 1024 * 1024, // 5MB máximo
      multiples: false
    });

    return new Promise((resolve, reject) => {
      form.parse(req, async (err, fields, files) => {
        if (err) {
          console.error('❌ Error parsing form:', err);
          return res.status(500).json({
            success: false,
            message: 'Error procesando el formulario',
            error: err.message
          });
        }

        try {
          console.log('🔍 Debug - Fields:', fields);
          console.log('🔍 Debug - Files:', files);

          const usuarioId = Array.isArray(fields.usuarioId) ? fields.usuarioId[0] : fields.usuarioId;
          const foto = Array.isArray(files.foto) ? files.foto[0] : files.foto;

          console.log('🔍 Debug - Usuario ID:', usuarioId);
          console.log('🔍 Debug - Foto:', foto ? foto.originalFilename : 'No hay foto');

          if (!usuarioId || !foto) {
            return res.status(400).json({
              success: false,
              message: 'Usuario ID y foto son requeridos'
            });
          }

          // Validar tipo de archivo
          const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
          if (!allowedTypes.includes(foto.mimetype)) {
            return res.status(400).json({
              success: false,
              message: 'Tipo de archivo no permitido. Solo JPG, PNG y GIF'
            });
          }

          // Generar nombre único para el archivo
          const extension = path.extname(foto.originalFilename);
          const nombreArchivo = `perfil_${usuarioId}_${Date.now()}${extension}`;
          const rutaFinal = path.join(uploadDir, nombreArchivo);

          // Mover archivo a la ubicación final
          fs.renameSync(foto.filepath, rutaFinal);

          // Actualizar base de datos
          const { db } = await connectToDatabase();
          const collection = db.collection('usuarios');

          const resultado = await collection.updateOne(
            { _id: new ObjectId(usuarioId) },
            {
              $set: {
                fotoPerfil: `/uploads/perfiles/${nombreArchivo}`,
                fechaActualizacion: new Date().toISOString()
              }
            }
          );

          if (resultado.modifiedCount > 0) {
            return res.status(200).json({
              success: true,
              message: 'Foto de perfil actualizada exitosamente',
              fotoPerfil: `/uploads/perfiles/${nombreArchivo}`
            });
          } else {
            return res.status(404).json({
              success: false,
              message: 'Usuario no encontrado'
            });
          }

        } catch (error) {
          console.error('❌ Error interno:', error);
          return res.status(500).json({
            success: false,
            message: 'Error interno del servidor',
            error: error.message
          });
        }
      });
    });

  } catch (error) {
    console.error('❌ Error general:', error);
    return res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
      error: error.message
    });
  }
}

export default async function handler(req, res) {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Método no permitido' });
  }

  let client;

  try {
    const { nombre, email, telefono, edad, pais, password } = req.body;

    console.log('📝 REGISTRO - Datos recibidos:', {
      nombre: !!nombre,
      email: !!email,
      telefono: !!telefono,
      edad: !!edad,
      pais: !!pais,
      password: !!password
    });

    // Validar datos requeridos
    if (!nombre || !email || !telefono || !edad || !pais || !password) {
      console.log('❌ Faltan campos requeridos');
      return res.status(400).json({
        success: false,
        message: 'Todos los campos son requeridos'
      });
    }

    // Validar contraseña
    if (password.length < 6) {
      console.log('❌ Contraseña muy corta');
      return res.status(400).json({
        success: false,
        message: 'La contraseña debe tener al menos 6 caracteres'
      });
    }

    console.log('🔄 Intentando conectar a MongoDB...');

    // Importar MongoDB dinámicamente
    const { MongoClient } = await import('mongodb');
    const bcrypt = await import('bcryptjs');

    const uri = process.env.MONGODB_URI || "mongodb+srv://jhon:<EMAIL>/?retryWrites=true&w=majority&appName=LA-LEGENDARIA-GERMAYORI";

    client = new MongoClient(uri, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 60000,
      maxPoolSize: 10,
      retryWrites: true,
      w: 'majority'
    });

    await client.connect();
    console.log('✅ MongoDB conectado exitosamente');

    const db = client.db(process.env.MONGODB_DB || 'legendaria-germayori');
    const collection = db.collection('usuarios');

    // Verificar si el email ya existe
    const usuarioExistente = await collection.findOne({ correo: email });
    if (usuarioExistente) {
      await client.close();
      return res.status(400).json({ 
        success: false, 
        message: 'Este email ya está registrado' 
      });
    }

    // Encriptar contraseña
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Crear fechas
    const fechaActual = new Date();
    const fechaExpiracion = new Date();
    fechaExpiracion.setDate(fechaActual.getDate() + 30);

    // Crear nuevo usuario
    const nuevoUsuario = {
      nombre: nombre.trim(),
      correo: email.toLowerCase().trim(),
      password: hashedPassword,
      celular: telefono.trim(),
      edad: parseInt(edad),
      pais: pais,
      fechaRegistro: fechaActual.toISOString(),
      plan: "PREMIUM",
      estado: "inactivo",
      fechaExpiracion: null,
      fechaPagoEsperada: fechaExpiracion.toISOString(),
      rol: "usuario",
      pagado: false,
      fechaPago: null,
      activadoPor: null,
      fechaActivacion: null
    };

    // Insertar en MongoDB
    const resultado = await collection.insertOne(nuevoUsuario);

    await client.close();

    if (resultado.insertedId) {
      return res.status(201).json({
        success: true,
        message: 'Usuario registrado exitosamente',
        userId: resultado.insertedId,
        nombre: nuevoUsuario.nombre,
        email: nuevoUsuario.correo,
        fechaExpiracion: fechaExpiracion.toISOString()
      });
    } else {
      throw new Error('No se pudo insertar el usuario');
    }

  } catch (error) {
    console.error('❌ Error en registro:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  } finally {
    // Cerrar conexión
    if (client) {
      try {
        await client.close();
        console.log('🔒 Conexión MongoDB cerrada');
      } catch (closeError) {
        console.error('Error cerrando conexión:', closeError);
      }
    }
  }
}

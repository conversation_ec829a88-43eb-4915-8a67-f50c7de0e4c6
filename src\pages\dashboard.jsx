// Archivo: src/pages/dashboard.jsx

import Head from 'next/head';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import Image from "next/image"
export default function Dashboard() {
  const [seccionActiva, setSeccionActiva] = useState('inicio');
  const [usuario, setUsuario] = useState(null);
  const [cargando, setCargando] = useState(true);
  const router = useRouter();

  // Verificar autenticación al cargar
  useEffect(() => {
    const verificarAuth = () => {
      const usuarioGuardado = localStorage.getItem('usuario_germayori');

      if (!usuarioGuardado) {
        router.push('/login');
        return;
      }

      try {
        const datosUsuario = JSON.parse(usuarioGuardado);

        // 🚨 VERIFICAR ESTADO DEL USUARIO PRIMERO
        if (datosUsuario.estado !== 'activo') {
          localStorage.removeItem('usuario_germayori');
          alert('Tu cuenta no está activa. Debes completar el pago de $75 USD y enviar el comprobante al grupo de WhatsApp. Los administradores activarán tu cuenta en máximo 24 horas.');
          router.push('/login');
          return;
        }

        // Verificar si la suscripción no ha expirado (solo si tiene fecha de expiración)
        if (datosUsuario.fechaExpiracion) {
          const fechaActual = new Date();
          const fechaExpiracion = new Date(datosUsuario.fechaExpiracion);

          if (fechaActual > fechaExpiracion) {
            localStorage.removeItem('usuario_germayori');
            alert('Tu suscripción ha expirado. Renueva tu acceso.');
            router.push('/login');
            return;
          }
        }

        setUsuario(datosUsuario);
      } catch (error) {
        console.error('Error al verificar usuario:', error);
        localStorage.removeItem('usuario_germayori');
        router.push('/login');
        return;
      }

      setCargando(false);
    };

    verificarAuth();
  }, [router]);

  // Función para cerrar sesión COMPLETA
  const cerrarSesion = () => {
    // Limpiar localStorage completamente
    localStorage.clear();
    // Limpiar sessionStorage
    sessionStorage.clear();
    // Limpiar cookies
    document.cookie.split(";").forEach(function(c) {
      document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
    });
    // Redirigir al login
    router.push('/login');
  };

  // Mostrar loading mientras verifica
  if (cargando) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-900 to-purple-900">
        <div className="text-center text-white">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-xl">Verificando acceso...</p>
        </div>
      </div>
    );
  }

  // Si no hay usuario, no mostrar nada (se redirige)
  if (!usuario) {
    return null;
  }

  return (
    <div className="flex h-screen w-full text-white relative" style={{
      background: `
        linear-gradient(135deg, rgba(30, 58, 138, 0.3), rgba(67, 56, 202, 0.3)),
        radial-gradient(circle at 20% 20%, rgba(255, 165, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 69, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 80%, rgba(0, 255, 255, 0.05) 0%, transparent 50%),
        linear-gradient(45deg, #1e3a8a 0%, #3730a3 50%, #1e1b4b 100%)
      `,
      filter: 'brightness(1.4) contrast(1.3) saturate(1.5)'
    }}>
      {/* Sidebar */}
      <aside className="w-60 bg-blue-950 p-4 flex flex-col">
        <div className="mb-6">
          <div className="flex items-center gap-3 mb-3">
            <div className="flex-shrink-0">
              <Image
                src={usuario?.fotoPerfil || "/logo.png"}
                alt="Foto de Perfil"
                width={48}
                height={48}
                className="w-12 h-12 rounded-full border-2 border-orange-400 shadow-lg object-cover"
              />
            </div>
            <div className="flex-1 min-w-0">
              <h1 className="text-lg font-bold truncate">🚀 GERMAYORI</h1>
              <p className="text-sm text-gray-300 truncate">👤 {usuario?.nombre || 'Usuario'}</p>
            </div>
          </div>
          <div className="text-center">
            <p className="text-yellow-400 text-xs">⭐ {usuario?.plan || 'PREMIUM'}</p>
            <p className="text-green-400 text-xs">
              ✅ Activo hasta: {usuario?.fechaExpiracion ? new Date(usuario.fechaExpiracion).toLocaleDateString() : 'N/A'}
            </p>
          </div>

          {/* Botón Perfil */}
          <button
            onClick={() => setSeccionActiva('perfil')}
            className={`w-full mt-3 p-2 rounded-lg text-sm font-semibold transition-all ${
              seccionActiva === 'perfil'
                ? 'bg-orange-500 text-white'
                : 'bg-blue-800 hover:bg-blue-700 text-gray-300 hover:text-white'
            }`}
          >
            👤 Mi Perfil
          </button>

          {/* Botón Cerrar Sesión */}
          <button
            onClick={cerrarSesion}
            className="w-full mt-2 p-2 rounded-lg text-sm font-semibold transition-all bg-red-600 hover:bg-red-700 text-white"
          >
            🚪 Cerrar Sesión
          </button>
        </div>
        <nav className="space-y-2 text-sm">
          <button
            onClick={() => setSeccionActiva('inicio')}
            className={`w-full p-3 rounded-lg font-semibold transition-all ${
              seccionActiva === 'inicio'
                ? 'bg-cyan-500 text-white border-2 border-cyan-300'
                : 'bg-blue-800 hover:bg-blue-700 text-gray-300 hover:text-white border-2 border-transparent hover:border-cyan-400'
            }`}
          >
            🏠 Inicio
          </button>
          <button
            onClick={() => setSeccionActiva('chat')}
            className={`w-full p-3 rounded-lg font-semibold transition-all ${
              seccionActiva === 'chat'
                ? 'bg-cyan-500 text-white border-2 border-cyan-300'
                : 'bg-blue-800 hover:bg-blue-700 text-gray-300 hover:text-white border-2 border-transparent hover:border-cyan-400'
            }`}
          >
            💬 Chat Educativo
          </button>
          <button
            onClick={() => setSeccionActiva('trading')}
            className={`w-full p-3 rounded-lg font-semibold transition-all ${
              seccionActiva === 'trading'
                ? 'bg-cyan-500 text-white border-2 border-cyan-300'
                : 'bg-blue-800 hover:bg-blue-700 text-gray-300 hover:text-white border-2 border-transparent hover:border-cyan-400'
            }`}
          >
            📉 Trading en Vivo
          </button>
          <button
            onClick={() => setSeccionActiva('calculadora')}
            className={`w-full p-3 rounded-lg font-semibold transition-all ${
              seccionActiva === 'calculadora'
                ? 'bg-cyan-500 text-white border-2 border-cyan-300'
                : 'bg-blue-800 hover:bg-blue-700 text-gray-300 hover:text-white border-2 border-transparent hover:border-cyan-400'
            }`}
          >
            🧮 Calculadora
          </button>

          <button
            onClick={() => setSeccionActiva('senales')}
            className={`w-full p-3 rounded-lg font-semibold transition-all ${
              seccionActiva === 'senales'
                ? 'bg-cyan-500 text-white border-2 border-cyan-300'
                : 'bg-blue-800 hover:bg-blue-700 text-gray-300 hover:text-white border-2 border-transparent hover:border-cyan-400'
            }`}
          >
            📡 Señales
          </button>
          <button
            onClick={() => setSeccionActiva('alertas')}
            className={`w-full p-3 rounded-lg font-semibold transition-all ${
              seccionActiva === 'alertas'
                ? 'bg-cyan-500 text-white border-2 border-cyan-300'
                : 'bg-blue-800 hover:bg-blue-700 text-gray-300 hover:text-white border-2 border-transparent hover:border-cyan-400'
            }`}
          >
            ⚠️ Alertas Mercado
          </button>
          <button
            onClick={() => setSeccionActiva('tradingview')}
            className={`w-full p-3 rounded-lg font-semibold transition-all ${
              seccionActiva === 'tradingview'
                ? 'bg-cyan-500 text-white border-2 border-cyan-300'
                : 'bg-blue-800 hover:bg-blue-700 text-gray-300 hover:text-white border-2 border-transparent hover:border-cyan-400'
            }`}
          >
            📉 TradingView
          </button>
          <p className="mt-4 text-xs text-gray-400">🎓 EDUCACIÓN GERMAYORI</p>
          <button
            onClick={() => setSeccionActiva('mentoria')}
            className={`w-full p-3 rounded-lg font-semibold transition-all ${
              seccionActiva === 'mentoria'
                ? 'bg-pink-500 text-white border-2 border-pink-300'
                : 'bg-blue-800 hover:bg-blue-700 text-gray-300 hover:text-white border-2 border-transparent hover:border-pink-400'
            }`}
          >
            🎓 Mentoría Directa <span className="bg-pink-600 text-white rounded px-2 text-xs ml-1">VIP</span>
          </button>
          <button
            onClick={() => setSeccionActiva('inversiones')}
            className={`w-full p-3 rounded-lg font-semibold transition-all ${
              seccionActiva === 'inversiones'
                ? 'bg-green-500 text-white border-2 border-green-300'
                : 'bg-blue-800 hover:bg-blue-700 text-gray-300 hover:text-white border-2 border-transparent hover:border-green-400'
            }`}
          >
            💰 Inversiones 4% <span className="bg-green-600 text-white rounded px-2 text-xs ml-1">NUEVO</span>
          </button>

          {/* Panel de Administración - Solo para administradores */}
          {usuario?.rol === 'administrador' && (
            <>
              <p className="mt-4 text-xs text-red-400">🔐 PANEL ADMINISTRADOR</p>
              <button
                onClick={() => setSeccionActiva('admin-usuarios')}
                className={`w-full p-3 rounded-lg font-semibold transition-all ${
                  seccionActiva === 'admin-usuarios'
                    ? 'bg-red-500 text-white border-2 border-red-300'
                    : 'bg-blue-800 hover:bg-blue-700 text-gray-300 hover:text-white border-2 border-transparent hover:border-red-400'
                }`}
              >
                👥 Gestionar Usuarios <span className="bg-red-600 text-white rounded px-2 text-xs ml-1">ADMIN</span>
              </button>
              <button
                onClick={() => setSeccionActiva('admin-inversiones')}
                className={`w-full p-3 rounded-lg font-semibold transition-all ${
                  seccionActiva === 'admin-inversiones'
                    ? 'bg-red-500 text-white border-2 border-red-300'
                    : 'bg-blue-800 hover:bg-blue-700 text-gray-300 hover:text-white border-2 border-transparent hover:border-red-400'
                }`}
              >
                💼 Gestionar Inversiones <span className="bg-red-600 text-white rounded px-2 text-xs ml-1">ADMIN</span>
              </button>
            </>
          )}
        </nav>
      </aside>

      {/* Main Content */}
      <main className="flex-1 p-8 overflow-auto">
        <Head>
          <title>Dashboard Germayori</title>
        </Head>

        {seccionActiva === 'inicio' && <SeccionInicio setSeccionActiva={setSeccionActiva} />}
        {seccionActiva === 'perfil' && <SeccionPerfil usuario={usuario} cerrarSesion={cerrarSesion} />}
        {seccionActiva === 'senales' && <SeccionSenales />}
        {seccionActiva === 'chat' && <SeccionChat />}
        {seccionActiva === 'trading' && <SeccionTrading />}
        {seccionActiva === 'calculadora' && <SeccionCalculadora />}
        {seccionActiva === 'alertas' && <SeccionAlertas />}
        {seccionActiva === 'tradingview' && <SeccionTradingView />}
        {seccionActiva === 'mentoria' && <SeccionMentoria />}
        {seccionActiva === 'inversiones' && <SeccionInversiones />}
        {seccionActiva === 'admin-usuarios' && usuario?.rol === 'administrador' && <SeccionAdminUsuarios />}
        {seccionActiva === 'admin-inversiones' && usuario?.rol === 'administrador' && <SeccionAdminInversiones />}
      </main>
    </div>
  );
}

// Componente Perfil de Usuario
function SeccionPerfil({ usuario: usuarioAuth, cerrarSesion }) {
  const [editandoPerfil, setEditandoPerfil] = useState(false);
  const [cambiandoPassword, setCambiandoPassword] = useState(false);
  const [subiendoFoto, setSubiendoFoto] = useState(false);
  const [mensaje, setMensaje] = useState('');
  const [datosEdicion, setDatosEdicion] = useState({
    nombre: usuarioAuth?.nombre || '',
    celular: usuarioAuth?.celular || '',
    edad: usuarioAuth?.edad || '',
    pais: usuarioAuth?.pais || ''
  });
  const [passwordData, setPasswordData] = useState({
    passwordActual: '',
    passwordNueva: '',
    confirmarPassword: ''
  });

  // Datos del usuario autenticado + datos simulados de trading
  const usuario = {
    id: usuarioAuth?.id || usuarioAuth?._id,
    nombre: usuarioAuth?.nombre || "Usuario GERMAYORI",
    email: usuarioAuth?.correo || usuarioAuth?.email || "<EMAIL>",
    plan: usuarioAuth?.plan || "PREMIUM",
    fechaRegistro: usuarioAuth?.fechaRegistro ? new Date(usuarioAuth.fechaRegistro).toLocaleDateString() : "15 Dic 2024",
    fechaExpiracion: usuarioAuth?.fechaExpiracion ? new Date(usuarioAuth.fechaExpiracion).toLocaleDateString() : "N/A",
    ultimoAcceso: "Hoy " + new Date().toLocaleTimeString(),
    estado: usuarioAuth?.estado || "activo",
    fotoPerfil: usuarioAuth?.fotoPerfil,
    celular: usuarioAuth?.celular || "N/A",
    edad: usuarioAuth?.edad || "N/A",
    pais: usuarioAuth?.pais || "N/A",
    // Datos simulados de trading (después se pueden obtener de MongoDB)
    operacionesTotales: 47,
    operacionesGanadas: 32,
    operacionesPerdidas: 15,
    winRate: 68,
    pipsGanados: 1250,
    balanceInicial: 1000,
    balanceActual: 1850,
    gananciaTotal: 850,
    racha: 5,
    tiempoActivo: "23 días"
  };

  // Función para subir foto
  const subirFoto = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    console.log('🔍 Debug Frontend - Usuario:', usuario);
    console.log('🔍 Debug Frontend - Usuario ID:', usuario.id);
    console.log('🔍 Debug Frontend - Archivo:', file);

    setSubiendoFoto(true);
    setMensaje('');

    const formData = new FormData();
    formData.append('foto', file);
    formData.append('usuarioId', usuario.id);

    try {
      const response = await fetch('/api/perfil/subir-foto', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();
      console.log('🔍 Debug Frontend - Respuesta:', data);

      if (data.success) {
        setMensaje('¡Foto actualizada exitosamente!');
        // Actualizar localStorage
        const usuarioActualizado = { ...usuario, fotoPerfil: data.fotoPerfil };
        localStorage.setItem('usuario_germayori', JSON.stringify(usuarioActualizado));
        // Recargar página para mostrar nueva foto
        window.location.reload();
      } else {
        setMensaje(data.message);
      }
    } catch (error) {
      setMensaje('Error subiendo foto');
    } finally {
      setSubiendoFoto(false);
    }
  };

  // Función para cambiar contraseña
  const cambiarPassword = async () => {
    if (passwordData.passwordNueva !== passwordData.confirmarPassword) {
      setMensaje('Las contraseñas no coinciden');
      return;
    }

    try {
      const response = await fetch('/api/perfil/cambiar-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          usuarioId: usuario.id,
          passwordActual: passwordData.passwordActual,
          passwordNueva: passwordData.passwordNueva
        })
      });

      const data = await response.json();
      if (data.success) {
        setMensaje('¡Contraseña actualizada exitosamente!');
        setCambiandoPassword(false);
        setPasswordData({ passwordActual: '', passwordNueva: '', confirmarPassword: '' });
      } else {
        setMensaje(data.message);
      }
    } catch (error) {
      setMensaje('Error cambiando contraseña');
    }
  };

  // Función para actualizar datos del perfil
  const actualizarDatos = async () => {
    try {
      const response = await fetch('/api/perfil/actualizar-datos', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          usuarioId: usuario.id,
          ...datosEdicion
        })
      });

      const data = await response.json();
      if (data.success) {
        setMensaje('¡Datos actualizados exitosamente!');
        setEditandoPerfil(false);
        // Actualizar localStorage
        localStorage.setItem('usuario_germayori', JSON.stringify(data.usuario));
        // Recargar página para mostrar cambios
        window.location.reload();
      } else {
        setMensaje(data.message);
      }
    } catch (error) {
      setMensaje('Error actualizando datos');
    }
  };

  return (
    <>
      <h2 className="text-3xl font-bold mb-6">👤 Mi Perfil</h2>

      {/* Mensaje de estado */}
      {mensaje && (
        <div className={`mb-6 p-4 rounded-lg text-center font-semibold ${
          mensaje.includes('exitosamente')
            ? 'bg-green-100 text-green-800 border border-green-300'
            : 'bg-red-100 text-red-800 border border-red-300'
        }`}>
          {mensaje}
        </div>
      )}

      {/* Información Personal */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        {/* Tarjeta Principal */}
        <div className="lg:col-span-1 bg-gradient-to-br from-blue-800 to-purple-800 rounded-xl p-6 text-center">
          <div className="mb-4 relative">
            {usuario.fotoPerfil ? (
              <Image
                src={usuario.fotoPerfil}
                alt="Foto de Perfil"
                width={96}
                height={96}
                className="w-24 h-24 rounded-full mx-auto object-cover border-4 border-orange-400 shadow-lg"
              />
            ) : (
              <div className="w-24 h-24 bg-gradient-to-br from-orange-400 to-red-500 rounded-full mx-auto flex items-center justify-center text-3xl font-bold border-4 border-orange-400 shadow-lg">
                {usuario.nombre.charAt(0)}
              </div>
            )}
            {/* Botón para cambiar foto */}
            <label className="absolute bottom-0 right-1/2 transform translate-x-1/2 translate-y-2 bg-orange-500 hover:bg-orange-600 text-white rounded-full p-2 cursor-pointer shadow-lg transition-all">
              <input
                type="file"
                accept="image/*"
                onChange={subirFoto}
                className="hidden"
                disabled={subiendoFoto}
              />
              {subiendoFoto ? '🔄' : '📷'}
            </label>
          </div>
          <h3 className="text-xl font-bold mb-2">{usuario.nombre}</h3>
          <p className="text-gray-300 mb-2">{usuario.email}</p>
          <div className="inline-flex items-center bg-yellow-500 text-black px-3 py-1 rounded-full text-sm font-semibold">
            ⭐ {usuario.plan}
          </div>
          <div className="mt-4 space-y-2 text-sm">
            <p>📅 Miembro desde: {usuario.fechaRegistro}</p>
            <p>📱 Celular: {usuario.celular}</p>
            <p>🎂 Edad: {usuario.edad}</p>
            <p>🌍 País: {usuario.pais}</p>
            <p>🕐 Último acceso: {usuario.ultimoAcceso}</p>
            <p>⏱️ Tiempo activo: {usuario.tiempoActivo}</p>
            <p className="text-green-400">✅ Activo hasta: {usuario.fechaExpiracion}</p>
            <p className={`font-semibold ${usuario.estado === 'activo' ? 'text-green-400' : 'text-red-400'}`}>
              🔘 Estado: {usuario.estado.toUpperCase()}
            </p>
          </div>
        </div>

        {/* Estadísticas de Trading */}
        <div className="lg:col-span-2 bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">📊 Estadísticas de Trading</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-900 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-green-400">{usuario.operacionesTotales}</div>
              <div className="text-sm text-gray-300">Operaciones</div>
            </div>
            <div className="bg-blue-900 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-green-400">{usuario.operacionesGanadas}</div>
              <div className="text-sm text-gray-300">Ganadas</div>
            </div>
            <div className="bg-blue-900 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-red-400">{usuario.operacionesPerdidas}</div>
              <div className="text-sm text-gray-300">Perdidas</div>
            </div>
            <div className="bg-blue-900 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-yellow-400">{usuario.winRate}%</div>
              <div className="text-sm text-gray-300">Win Rate</div>
            </div>
          </div>
        </div>
      </div>

      {/* Rendimiento Financiero */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">💰 Rendimiento Financiero</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span>Balance Inicial:</span>
              <span className="font-semibold">${usuario.balanceInicial}</span>
            </div>
            <div className="flex justify-between items-center">
              <span>Balance Actual:</span>
              <span className="font-semibold text-green-400">${usuario.balanceActual}</span>
            </div>
            <div className="flex justify-between items-center">
              <span>Ganancia Total:</span>
              <span className="font-semibold text-green-400">+${usuario.gananciaTotal}</span>
            </div>
            <div className="flex justify-between items-center">
              <span>Pips Ganados:</span>
              <span className="font-semibold text-blue-300">+{usuario.pipsGanados} pips</span>
            </div>
            <div className="flex justify-between items-center">
              <span>Racha Actual:</span>
              <span className="font-semibold text-yellow-400">{usuario.racha} operaciones</span>
            </div>
          </div>
        </div>

        {/* Configuración de Cuenta */}
        <div className="bg-white rounded-xl p-6 border-2 border-blue-600">
          <h3 className="text-xl font-semibold mb-4 text-blue-800">⚙️ Configuración</h3>

          {/* Formulario de edición de perfil */}
          {editandoPerfil ? (
            <div className="bg-white rounded-lg p-4 space-y-4">
              <h4 className="font-semibold text-orange-600">✏️ Editar Datos Personales</h4>
              <input
                type="text"
                value={datosEdicion.nombre}
                onChange={(e) => setDatosEdicion({...datosEdicion, nombre: e.target.value})}
                className="w-full p-3 rounded-lg text-black border-2 border-gray-300 focus:border-orange-500 focus:outline-none"
                placeholder="Nombre completo"
              />
              <input
                type="text"
                value={datosEdicion.celular}
                onChange={(e) => setDatosEdicion({...datosEdicion, celular: e.target.value})}
                className="w-full p-3 rounded-lg text-black border-2 border-gray-300 focus:border-orange-500 focus:outline-none"
                placeholder="Número de celular"
              />
              <input
                type="number"
                value={datosEdicion.edad}
                onChange={(e) => setDatosEdicion({...datosEdicion, edad: e.target.value})}
                className="w-full p-3 rounded-lg text-black border-2 border-gray-300 focus:border-orange-500 focus:outline-none"
                placeholder="Edad"
              />
              <input
                type="text"
                value={datosEdicion.pais}
                onChange={(e) => setDatosEdicion({...datosEdicion, pais: e.target.value})}
                className="w-full p-3 rounded-lg text-black border-2 border-gray-300 focus:border-orange-500 focus:outline-none"
                placeholder="País"
              />
              <div className="flex gap-2">
                <button
                  onClick={actualizarDatos}
                  className="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold transition-colors"
                >
                  ✅ Guardar
                </button>
                <button
                  onClick={() => setEditandoPerfil(false)}
                  className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-3 rounded-lg font-semibold transition-colors"
                >
                  ❌ Cancelar
                </button>
              </div>
            </div>
          ) : cambiandoPassword ? (
            <div className="bg-white rounded-lg p-4 space-y-4">
              <h4 className="font-semibold text-blue-600">🔒 Cambiar Contraseña</h4>
              <input
                type="password"
                value={passwordData.passwordActual}
                onChange={(e) => setPasswordData({...passwordData, passwordActual: e.target.value})}
                className="w-full p-3 rounded-lg text-black border-2 border-gray-300 focus:border-blue-500 focus:outline-none"
                placeholder="Contraseña actual"
              />
              <input
                type="password"
                value={passwordData.passwordNueva}
                onChange={(e) => setPasswordData({...passwordData, passwordNueva: e.target.value})}
                className="w-full p-3 rounded-lg text-black border-2 border-gray-300 focus:border-blue-500 focus:outline-none"
                placeholder="Nueva contraseña"
              />
              <input
                type="password"
                value={passwordData.confirmarPassword}
                onChange={(e) => setPasswordData({...passwordData, confirmarPassword: e.target.value})}
                className="w-full p-3 rounded-lg text-black border-2 border-gray-300 focus:border-blue-500 focus:outline-none"
                placeholder="Confirmar nueva contraseña"
              />
              <div className="flex gap-2">
                <button
                  onClick={cambiarPassword}
                  className="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold transition-colors"
                >
                  ✅ Cambiar
                </button>
                <button
                  onClick={() => setCambiandoPassword(false)}
                  className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-3 rounded-lg font-semibold transition-colors"
                >
                  ❌ Cancelar
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <button
                onClick={() => setEditandoPerfil(true)}
                className="w-full bg-orange-500 hover:bg-orange-600 text-white py-3 rounded-lg font-semibold transition-colors"
              >
                ✏️ Editar Perfil
              </button>
              <button
                onClick={() => setCambiandoPassword(true)}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold transition-colors"
              >
                🔒 Cambiar Contraseña
              </button>
              <button className="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold transition-colors">
                💳 Gestionar Suscripción
              </button>
              <button
                onClick={cerrarSesion}
                className="w-full bg-red-600 hover:bg-red-700 text-white py-3 rounded-lg font-semibold transition-colors"
              >
                🚪 Cerrar Sesión
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Actividad Reciente */}
      <div className="bg-blue-800 rounded-xl p-6">
        <h3 className="text-xl font-semibold mb-4">📈 Actividad Reciente</h3>
        <div className="space-y-3">
          <div className="bg-blue-900 rounded-lg p-4 flex justify-between items-center">
            <div>
              <p className="font-semibold text-green-400">✅ XAUUSD - Operación Cerrada</p>
              <p className="text-sm text-gray-300">+45 pips | +$125</p>
            </div>
            <span className="text-xs text-gray-400">Hace 2 horas</span>
          </div>
          <div className="bg-blue-900 rounded-lg p-4 flex justify-between items-center">
            <div>
              <p className="font-semibold text-blue-400">📊 Análisis Completado</p>
              <p className="text-sm text-gray-300">EURUSD - 5 temporalidades</p>
            </div>
            <span className="text-xs text-gray-400">Hace 4 horas</span>
          </div>
          <div className="bg-blue-900 rounded-lg p-4 flex justify-between items-center">
            <div>
              <p className="font-semibold text-yellow-400">🔔 Nueva Señal Recibida</p>
              <p className="text-sm text-gray-300">GBPUSD - Liquidez Superior</p>
            </div>
            <span className="text-xs text-gray-400">Hace 6 horas</span>
          </div>
        </div>
      </div>
    </>
  );
}

// Componente de la sección de inicio
function SeccionInicio({ setSeccionActiva }) {
  return (
    <>
      <h2 className="text-3xl font-bold mb-4">Bienvenido a GERMAYORI</h2>
      <p className="mb-6 text-sm">Selecciona un canal para comenzar</p>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card title="Chat Educativo" desc="Asistente IA Germayori" icon="💬" onClick={() => setSeccionActiva('chat')} />
        <Card title="Trading en Vivo" desc="Calendario Zoom con Educadores" icon="🎥" tag="LIVE" onClick={() => setSeccionActiva('trading')} />
        <Card title="Calculadora" desc="Pips, profit y riesgo" icon="🧮" onClick={() => setSeccionActiva('calculadora')} />
        <Card title="Señales" desc="Estrategia Liquidez Germayori" icon="📡" onClick={() => setSeccionActiva('senales')} />
        <Card title="TradingView" desc="Gráficos profesionales en tiempo real" icon="📊" tag="PROFESIONAL" onClick={() => setSeccionActiva('tradingview')} />
        <Card title="Mentoría Directa" desc="Con el creador de GERMAYORI" icon="🎓" tag="VIP EXCLUSIVO" onClick={() => setSeccionActiva('mentoria')} />
        <Card title="Inversiones 4%" desc="Invierte conmigo - 4% mensual" icon="💰" tag="NUEVO" onClick={() => setSeccionActiva('inversiones')} />
      </div>
    </>
  );
}

// Componente Canal de Señales en Tiempo Real
function SeccionSenales() {
  const [senales, setSenales] = useState([]);
  const [usuario, setUsuario] = useState(null);
  const [esAdmin, setEsAdmin] = useState(false);
  const [mostrarFormulario, setMostrarFormulario] = useState(false);
  const [cargando, setCargando] = useState(true);

  // Formulario para nueva señal
  const [nuevaSenal, setNuevaSenal] = useState({
    par: 'SPX500',
    tipo: 'BUY',
    entrada: '',
    sl: '',
    tp1: '',
    tp2: '',
    tp3: '',
    tp4: '',
    analisis: ''
  });

  // Verificar usuario y cargar señales
  useEffect(() => {
    const usuarioGuardado = localStorage.getItem('usuario_germayori');
    if (usuarioGuardado) {
      const datosUsuario = JSON.parse(usuarioGuardado);
      setUsuario(datosUsuario);

      // Verificar si es administrador autorizado para crear señales
      if (datosUsuario.correo === '<EMAIL>' ||
          datosUsuario.correo === '<EMAIL>' ||
          datosUsuario.correo === '<EMAIL>') {
        setEsAdmin(true);
      }
    }

    cargarSenales();
  }, []);

  // Cargar señales desde el servidor
  const cargarSenales = async () => {
    try {
      setCargando(true);
      const response = await fetch('/api/senales');
      const data = await response.json();

      if (data.success) {
        setSenales(data.senales || []);
      }
    } catch (error) {
      console.error('Error cargando señales:', error);
      // Señales de ejemplo si no hay conexión
      setSenales([
        {
          id: 1,
          par: 'SPX500',
          tipo: 'SELL',
          entrada: '6250.00',
          sl: '6275.00',
          tp1: '6230.00',
          tp2: '6200.00',
          tp3: '6185.00',
          tp4: '6150.00',
          estado: 'ACTIVA',
          fecha: new Date().toISOString(),
          analisis: 'Análisis Técnico Multitemporal (Smart Money + Liquidez): 🔴 Temporalidad D1 📊 Se observa que el precio mitiga un FVG anterior y ahora regresa con intención de redistribuir. 🔴 CHoCH bajista reciente + FQH Bajista. 🔴 Zona de interés de venta marcada: arriba (zona roja) en 6,300.00. 🔴 Temporalidad H1 📊 Se liquida FQH y se genera CHoCH bajista. 🔴 El precio reacciona desde una FVG bajista y reacciona con estructura débil. 🔴 Acabo de romper zona de demanda menor = indicio claro de redistribución.'
        },
        {
          id: 2,
          par: 'XAUUSD',
          tipo: 'BUY',
          entrada: '3523.50',
          sl: '3515.00',
          tp1: '3536.00',
          tp2: '3550.00',
          tp3: '3575.00',
          tp4: '3400.00',
          estado: 'ACTIVA',
          fecha: new Date(Date.now() - 3600000).toISOString(),
          analisis: 'Análisis:'
        }
      ]);
    } finally {
      setCargando(false);
    }
  };

  // Enviar nueva señal
  const enviarSenal = async (e) => {
    e.preventDefault();

    try {
      const response = await fetch('/api/senales', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...nuevaSenal,
          usuarioEmail: usuario.correo || usuario.email,
          fecha: new Date().toISOString(),
          estado: 'ACTIVA'
        })
      });

      const data = await response.json();

      if (data.success) {
        // Agregar la nueva señal al estado local
        setSenales(prev => [data.senal, ...prev]);

        // Limpiar formulario
        setNuevaSenal({
          par: 'SPX500',
          tipo: 'BUY',
          entrada: '',
          sl: '',
          tp1: '',
          tp2: '',
          tp3: '',
          tp4: '',
          analisis: ''
        });

        setMostrarFormulario(false);
        alert('✅ Señal enviada exitosamente');
      } else {
        alert('❌ Error enviando señal: ' + data.message);
      }
    } catch (error) {
      console.error('Error:', error);
      alert('❌ Error de conexión');
    }
  };

  // Formatear fecha
  const formatearFecha = (fecha) => {
    const date = new Date(fecha);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  // Obtener color según tipo de señal
  const obtenerColorSenal = (tipo) => {
    return tipo === 'BUY' ? 'border-green-500 bg-green-900/20' : 'border-red-500 bg-red-900/20';
  };

  return (
    <>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-3xl font-bold">📡 SEÑALES DEL CANAL</h2>
        {esAdmin && (
          <button
            onClick={() => setMostrarFormulario(!mostrarFormulario)}
            className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
          >
            {mostrarFormulario ? '❌ Cancelar' : '➕ Nueva Señal'}
          </button>
        )}
      </div>

      {/* Formulario para administradores - Estilo como en la imagen */}
      {esAdmin && mostrarFormulario && (
        <div className="bg-gray-900 rounded-lg p-6 mb-6 border border-gray-700">
          <div className="bg-yellow-600 text-black px-4 py-2 rounded-t-lg font-bold text-center mb-4">
            ENVIAR SEÑAL
          </div>

          <form onSubmit={enviarSenal} className="space-y-4">
            {/* Activo */}
            <div>
              <label className="block text-yellow-400 text-sm font-bold mb-2">ACTIVO</label>
              <input
                type="text"
                value={nuevaSenal.par}
                onChange={(e) => setNuevaSenal({...nuevaSenal, par: e.target.value})}
                className="w-full p-3 rounded bg-gray-800 text-white border border-gray-600 placeholder-gray-400"
                placeholder="Ej: XAUUSD, EURUSD, BTCUSD"
                required
              />
            </div>

            {/* Dirección */}
            <div>
              <label className="block text-yellow-400 text-sm font-bold mb-2">DIRECCIÓN</label>
              <select
                value={nuevaSenal.tipo}
                onChange={(e) => setNuevaSenal({...nuevaSenal, tipo: e.target.value})}
                className="w-full p-3 rounded bg-gray-800 text-white border border-gray-600"
              >
                <option value="">Seleccionar</option>
                <option value="BUY">BUY</option>
                <option value="SELL">SELL</option>
              </select>
            </div>

            {/* Entrada y Stop Loss */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-yellow-400 text-sm font-bold mb-2">ENTRADA</label>
                <input
                  type="text"
                  value={nuevaSenal.entrada}
                  onChange={(e) => setNuevaSenal({...nuevaSenal, entrada: e.target.value})}
                  className="w-full p-3 rounded bg-gray-800 text-white border border-gray-600 placeholder-gray-400"
                  placeholder="Precio"
                  required
                />
              </div>
              <div>
                <label className="block text-yellow-400 text-sm font-bold mb-2">STOP LOSS</label>
                <input
                  type="text"
                  value={nuevaSenal.sl}
                  onChange={(e) => setNuevaSenal({...nuevaSenal, sl: e.target.value})}
                  className="w-full p-3 rounded bg-red-800 text-white border border-red-600 placeholder-gray-400"
                  placeholder="SL"
                  required
                />
              </div>
            </div>

            {/* Take Profits */}
            <div>
              <label className="block text-yellow-400 text-sm font-bold mb-2">TAKE PROFITS</label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                <input
                  type="text"
                  value={nuevaSenal.tp1}
                  onChange={(e) => setNuevaSenal({...nuevaSenal, tp1: e.target.value})}
                  className="p-3 rounded bg-green-800 text-white border border-green-600 placeholder-gray-400"
                  placeholder="TP1"
                  required
                />
                <input
                  type="text"
                  value={nuevaSenal.tp2}
                  onChange={(e) => setNuevaSenal({...nuevaSenal, tp2: e.target.value})}
                  className="p-3 rounded bg-green-800 text-white border border-green-600 placeholder-gray-400"
                  placeholder="TP2"
                />
                <input
                  type="text"
                  value={nuevaSenal.tp3}
                  onChange={(e) => setNuevaSenal({...nuevaSenal, tp3: e.target.value})}
                  className="p-3 rounded bg-green-800 text-white border border-green-600 placeholder-gray-400"
                  placeholder="TP3"
                />
                <input
                  type="text"
                  value={nuevaSenal.tp4}
                  onChange={(e) => setNuevaSenal({...nuevaSenal, tp4: e.target.value})}
                  className="p-3 rounded bg-green-800 text-white border border-green-600 placeholder-gray-400"
                  placeholder="TP4"
                />
              </div>
            </div>

            {/* Análisis */}
            <div>
              <label className="block text-yellow-400 text-sm font-bold mb-2">ANÁLISIS</label>
              <textarea
                value={nuevaSenal.analisis}
                onChange={(e) => setNuevaSenal({...nuevaSenal, analisis: e.target.value})}
                className="w-full p-3 rounded bg-gray-800 text-white border border-gray-600 h-24 placeholder-gray-400"
                placeholder="Describe el análisis técnico..."
              />
            </div>

            {/* Botón enviar */}
            <button
              type="submit"
              className="w-full bg-yellow-600 hover:bg-yellow-700 text-black font-bold py-3 rounded transition-colors"
            >
              ENVIAR SEÑAL AL CANAL
            </button>
          </form>
        </div>
      )}

      {/* Lista de señales */}
      <div className="space-y-4">
        {cargando ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-orange-500 mx-auto mb-4"></div>
            <p className="text-xl">Cargando señales...</p>
          </div>
        ) : senales.length === 0 ? (
          <div className="bg-gray-800 rounded-xl p-8 text-center">
            <div className="text-6xl mb-4">📡</div>
            <h3 className="text-xl font-semibold mb-2">No hay señales disponibles</h3>
            <p className="text-gray-400">Las señales aparecerán aquí cuando sean enviadas</p>
          </div>
        ) : (
          senales.map((senal, index) => (
            <div key={senal.id || index} className={`bg-gray-800 rounded-xl p-6 border-l-4 ${obtenerColorSenal(senal.tipo)}`}>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4">
                  <div className={`px-3 py-1 rounded-full text-sm font-bold ${
                    senal.tipo === 'BUY' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
                  }`}>
                    {senal.par} {senal.tipo}
                  </div>
                  <div className="text-sm text-gray-400">
                    {formatearFecha(senal.fecha)}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-4">
                <div className="bg-gray-700 rounded-lg p-3">
                  <div className="text-xs text-gray-400 mb-1">Entrada</div>
                  <div className="font-bold text-white">{senal.entrada}</div>
                </div>
                <div className="bg-red-900/30 rounded-lg p-3">
                  <div className="text-xs text-red-400 mb-1">SL</div>
                  <div className="font-bold text-red-300">{senal.sl}</div>
                </div>
                <div className="bg-green-900/30 rounded-lg p-3">
                  <div className="text-xs text-green-400 mb-1">TP1</div>
                  <div className="font-bold text-green-300">{senal.tp1}</div>
                </div>
                <div className="bg-green-900/30 rounded-lg p-3">
                  <div className="text-xs text-green-400 mb-1">TP2</div>
                  <div className="font-bold text-green-300">{senal.tp2 || '-'}</div>
                </div>
                <div className="bg-green-900/30 rounded-lg p-3">
                  <div className="text-xs text-green-400 mb-1">TP3</div>
                  <div className="font-bold text-green-300">{senal.tp3 || '-'}</div>
                </div>
                <div className="bg-green-900/30 rounded-lg p-3">
                  <div className="text-xs text-green-400 mb-1">TP4</div>
                  <div className="font-bold text-green-300">{senal.tp4 || '-'}</div>
                </div>
              </div>

              {senal.analisis && (
                <div className="bg-gray-700 rounded-lg p-4">
                  <div className="text-sm font-medium text-yellow-400 mb-2">📊 Análisis:</div>
                  <div className="text-sm text-gray-300 leading-relaxed">
                    {senal.analisis}
                  </div>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </>
  );
}

// Componente Chat Educativo
function SeccionChat() {
  const [mensajes, setMensajes] = useState([
    {
      texto: "¡Hola! Soy Germayori 🚀 Estoy aquí para enseñarte mi estrategia de liquidez institucional. ¿Qué quieres aprender hoy?",
      autor: "germayori"
    }
  ]);
  const [input, setInput] = useState("");

  const obtenerRespuestaGermayori = (pregunta) => {
    const preguntaLower = pregunta.toLowerCase();

    if (preguntaLower.includes('liquidez') || preguntaLower.includes('liquidity')) {
      return "🔻🔺 La liquidez es clave en mi estrategia:\n\n🔻 Liquidez Inferior: Cuando el precio liquida un bajo importante, buscamos COMPRA\n🔺 Liquidez Superior: Cuando el precio liquida un alto importante, buscamos VENTA\n\nRecuerda: Los grandes bancos necesitan liquidez para sus órdenes masivas. Nosotros seguimos su rastro.";
    }

    if (preguntaLower.includes('fvg') || preguntaLower.includes('fair value gap') || preguntaLower.includes('gap')) {
      return "📉 Fair Value Gap (FVG) es fundamental:\n\nDespués de liquidar la liquidez, NO entramos inmediatamente. Esperamos un retroceso claro hacia un FVG para una entrada más precisa.\n\nEl FVG es donde el precio 'debe' regresar según la lógica institucional. Es nuestro punto de entrada ideal.";
    }

    if (preguntaLower.includes('order block') || preguntaLower.includes('bloque')) {
      return "🚫 EVITA Order Blocks tradicionales:\n\nLos bloques de órdenes simples pueden fallar y no son confiables por sí solos. En mi estrategia nos enfocamos en:\n\n✅ Desequilibrios\n✅ Expansiones y retrocesos\n✅ Impulsos alineados con liquidez tomada\n\nEsto es más preciso que los OB tradicionales.";
    }

    if (preguntaLower.includes('institucional') || preguntaLower.includes('banco') || preguntaLower.includes('manipulacion')) {
      return "🏦 Estrategia Institucional:\n\nAnalizamos la acción del precio con lógica institucional (flujo de órdenes bancarias):\n\n📊 Desequilibrios - Donde hay más compradores o vendedores\n📈 Expansiones y retrocesos - Movimientos institucionales\n🎯 Impulsos alineados - Confirmación de dirección\n\nLos bancos mueven el mercado, nosotros los seguimos.";
    }

    if (preguntaLower.includes('temporalidad') || preguntaLower.includes('timeframe')) {
      return "⏰ Análisis Multi-Temporalidad:\n\nUsamos 5 temporalidades para confirmación:\n📅 Diario - Tendencia principal\n🕐 H4 - Estructura intermedia\n🕐 H1 - Confirmación\n⏰ 30min - Entrada precisa\n⚡ 5min - Timing exacto\n\nCada temporalidad debe alinearse para una señal fuerte.";
    }

    if (preguntaLower.includes('entrada') || preguntaLower.includes('entry')) {
      return "🎯 Proceso de Entrada:\n\n1️⃣ Identificar liquidez tomada (alto/bajo importante)\n2️⃣ Esperar retroceso hacia FVG\n3️⃣ Confirmar con análisis institucional\n4️⃣ Verificar alineación en múltiples temporalidades\n5️⃣ Ejecutar entrada con gestión de riesgo\n\n¡Paciencia es clave! No todas las liquidaciones son válidas.";
    }

    if (preguntaLower.includes('riesgo') || preguntaLower.includes('risk') || preguntaLower.includes('stop')) {
      return "⛔ Gestión de Riesgo Germayori:\n\n💡 Regla del 2% - Nunca arriesgues más del 2% por operación\n🎯 Risk/Reward 1:3 mínimo\n📍 Stop Loss debajo/encima de la liquidez tomada\n🎯 Take Profit en próximos niveles de liquidez\n\n¡El riesgo controlado es lo que separa a los profesionales!";
    }

    // Respuesta por defecto
    return "🤔 Interesante pregunta. Mi estrategia se basa en:\n\n🔻🔺 Liquidez institucional\n📉 Fair Value Gaps (FVG)\n🏦 Análisis de flujo bancario\n⏰ Múltiples temporalidades\n🚫 Sin Order Blocks tradicionales\n\n¿Sobre cuál de estos temas quieres que profundice?";
  };

  const enviarMensaje = () => {
    if (input.trim() === "") return;

    const nuevoMensaje = { texto: input, autor: "tú" };
    setMensajes([...mensajes, nuevoMensaje]);
    const pregunta = input;
    setInput("");

    setTimeout(() => {
      const respuesta = {
        texto: obtenerRespuestaGermayori(pregunta),
        autor: "germayori",
      };
      setMensajes((mensajesActuales) => [...mensajesActuales, respuesta]);
    }, 1500);
  };

  return (
    <>
      <h2 className="text-3xl font-bold mb-4">💬 Chat Educativo Germayori</h2>
      <div className="bg-blue-800 rounded-xl p-6 shadow-lg">
        <div className="h-96 overflow-y-auto space-y-3 mb-4 bg-gray-900 p-4 rounded-lg border border-blue-600">
          {mensajes.map((msg, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg max-w-4xl ${
                msg.autor === "germayori"
                  ? "bg-gradient-to-r from-indigo-600 to-purple-600 text-white text-left mr-8"
                  : "bg-gradient-to-r from-green-600 to-emerald-600 text-white text-right ml-8"
              }`}
            >
              <div className="font-semibold text-sm mb-1">
                {msg.autor === "germayori" ? "🚀 Germayori" : "👤 Tú"}
              </div>
              <div className="text-white whitespace-pre-line leading-relaxed">
                {msg.texto}
              </div>
            </div>
          ))}
        </div>
        <div className="flex gap-3">
          <input
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Pregúntame sobre liquidez, FVG, estrategia institucional..."
            className="flex-1 p-4 rounded-lg text-black font-medium border-2 border-blue-600 focus:border-orange-500 focus:outline-none"
            onKeyPress={(e) => e.key === 'Enter' && enviarMensaje()}
          />
          <button
            onClick={enviarMensaje}
            className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-8 py-4 rounded-lg font-bold transition-all transform hover:scale-105"
          >
            📤 Enviar
          </button>
        </div>
        <div className="mt-4 text-center">
          <p className="text-sm text-gray-300">
            💡 Pregunta sobre: <span className="text-orange-400">liquidez, FVG, estrategia institucional, temporalidades, gestión de riesgo</span>
          </p>
        </div>
      </div>
    </>
  );
}

// Componente Trading en Vivo - Calendario de Zoom Semanal
function SeccionTrading() {
  const [usuario, setUsuario] = useState(null);
  const [modoAdmin, setModoAdmin] = useState(false);
  const [editandoSesion, setEditandoSesion] = useState(null);
  const [nuevoEnlace, setNuevoEnlace] = useState('');

  // Verificar si el usuario es administrador
  useEffect(() => {
    const usuarioGuardado = localStorage.getItem('usuario_germayori');
    if (usuarioGuardado) {
      const datosUsuario = JSON.parse(usuarioGuardado);
      setUsuario(datosUsuario);
      // Solo administradores pueden administrar los enlaces
      if (datosUsuario.rol === 'administrador' || datosUsuario.correo === '<EMAIL>' || datosUsuario.nombre === 'GERMAYORI' || datosUsuario.correo === '<EMAIL>') {
        setModoAdmin(true);
      }
    }
  }, []);

  // Datos de ejemplo de educadores y sus horarios
  const educadores = [
    {
      nombre: "GERMAYORI",
      especialidad: "Liquidez Institucional",
      avatar: "🏆",
      color: "from-orange-500 to-red-600"
    },
    {
      nombre: "Carlos Mendez",
      especialidad: "Análisis Técnico",
      avatar: "📊",
      color: "from-blue-500 to-purple-600"
    },
    {
      nombre: "Ana Rodriguez",
      especialidad: "Psicología del Trading",
      avatar: "🧠",
      color: "from-green-500 to-teal-600"
    }
  ];

  // Horarios de sesiones semanales
  const sesionesSemanales = [
    {
      dia: "Lunes",
      hora: "20:00 GMT-5",
      educador: "GERMAYORI",
      tema: "Análisis Semanal de Mercados",
      enlaceZoom: "https://zoom.us/j/ejemplo1",
      activa: true
    },
    {
      dia: "Miércoles",
      hora: "19:00 GMT-5",
      educador: "Carlos Mendez",
      tema: "Análisis Técnico Avanzado",
      enlaceZoom: "https://zoom.us/j/ejemplo2",
      activa: true
    },
    {
      dia: "Viernes",
      hora: "18:00 GMT-5",
      educador: "Ana Rodriguez",
      tema: "Mentalidad y Disciplina",
      enlaceZoom: "https://zoom.us/j/ejemplo3",
      activa: false
    }
  ];

  const actualizarEnlace = (index) => {
    // Aquí iría la lógica para actualizar el enlace en la base de datos
    console.log(`Actualizando enlace para sesión ${index}: ${nuevoEnlace}`);
    setEditandoSesion(null);
    setNuevoEnlace('');
  };

  return (
    <>
      <h2 className="text-3xl font-bold mb-6">🎥 Trading en Vivo - Calendario Semanal</h2>

      {/* Información de educadores */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        {educadores.map((educador, index) => (
          <div key={index} className={`bg-gradient-to-br ${educador.color} rounded-xl p-6 text-center`}>
            <div className="text-4xl mb-3">{educador.avatar}</div>
            <h3 className="text-lg font-bold">{educador.nombre}</h3>
            <p className="text-sm opacity-90">{educador.especialidad}</p>
          </div>
        ))}
      </div>

      {/* Calendario de sesiones */}
      <div className="bg-blue-800 rounded-xl p-6">
        <h3 className="text-xl font-semibold mb-6">📅 Horario de Sesiones en Vivo</h3>
        <div className="space-y-4">
          {sesionesSemanales.map((sesion, index) => (
            <div key={index} className={`bg-blue-900 rounded-lg p-6 border-l-4 ${sesion.activa ? 'border-green-400' : 'border-gray-400'}`}>
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <span className="text-lg font-bold">{sesion.dia}</span>
                    <span className="bg-blue-700 px-3 py-1 rounded-full text-sm">{sesion.hora}</span>
                    <span className={`px-2 py-1 rounded-full text-xs ${sesion.activa ? 'bg-green-500' : 'bg-gray-500'}`}>
                      {sesion.activa ? '🟢 ACTIVA' : '🔴 INACTIVA'}
                    </span>
                  </div>
                  <h4 className="font-semibold text-lg mb-1">{sesion.tema}</h4>
                  <p className="text-gray-300">👨‍🏫 Educador: {sesion.educador}</p>
                </div>

                <div className="flex flex-col gap-2">
                  {sesion.activa ? (
                    <button
                      onClick={() => window.open(sesion.enlaceZoom, '_blank')}
                      className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
                    >
                      🚀 Unirse a Zoom
                    </button>
                  ) : (
                    <button
                      disabled
                      className="bg-gray-600 text-gray-300 px-6 py-3 rounded-lg font-semibold cursor-not-allowed"
                    >
                      ⏰ Próximamente
                    </button>
                  )}

                  {modoAdmin && (
                    <div className="flex gap-2">
                      {editandoSesion === index ? (
                        <div className="flex gap-2">
                          <input
                            type="text"
                            value={nuevoEnlace}
                            onChange={(e) => setNuevoEnlace(e.target.value)}
                            placeholder="Nuevo enlace de Zoom"
                            className="px-3 py-1 rounded text-black text-sm"
                          />
                          <button
                            onClick={() => actualizarEnlace(index)}
                            className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
                          >
                            ✅
                          </button>
                          <button
                            onClick={() => setEditandoSesion(null)}
                            className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
                          >
                            ❌
                          </button>
                        </div>
                      ) : (
                        <button
                          onClick={() => setEditandoSesion(index)}
                          className="bg-orange-500 hover:bg-orange-600 text-white px-3 py-1 rounded text-sm"
                        >
                          ✏️ Editar
                        </button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {modoAdmin && (
          <div className="mt-6 p-4 bg-red-900 rounded-lg border border-red-600">
            <p className="text-sm text-red-300">
              🔐 <strong>Modo Administrador:</strong> Puedes editar los enlaces de Zoom de las sesiones.
            </p>
          </div>
        )}
      </div>
    </>
  );
}

// Componente Calculadora
function SeccionCalculadora() {
  const [pips, setPips] = useState('');
  const [lotSize, setLotSize] = useState('');
  const [resultado, setResultado] = useState(null);

  const calcular = () => {
    if (pips && lotSize) {
      const profit = parseFloat(pips) * parseFloat(lotSize) * 10;
      setResultado(profit);
    }
  };

  return (
    <>
      <h2 className="text-3xl font-bold mb-4">🧮 Calculadora de Trading</h2>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">💰 Calculadora de Pips</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2 text-white">Pips:</label>
              <input
                type="number"
                value={pips}
                onChange={(e) => setPips(e.target.value)}
                className="w-full p-3 rounded text-black font-semibold border-2 border-blue-600 focus:border-orange-500 focus:outline-none"
                placeholder="Ej: 50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2 text-white">Tamaño del Lote:</label>
              <input
                type="number"
                step="0.01"
                value={lotSize}
                onChange={(e) => setLotSize(e.target.value)}
                className="w-full p-3 rounded text-black font-semibold border-2 border-blue-600 focus:border-orange-500 focus:outline-none"
                placeholder="Ej: 0.1"
              />
            </div>
            <button
              onClick={calcular}
              className="w-full bg-orange-500 hover:bg-orange-600 text-white py-3 rounded font-semibold"
            >
              Calcular Profit
            </button>
            {resultado !== null && (
              <div className="bg-blue-900 rounded p-4 text-center">
                <p className="text-lg font-semibold text-green-400">
                  Profit: ${resultado.toFixed(2)}
                </p>
              </div>
            )}
          </div>
        </div>
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">📊 Gestión de Riesgo</h3>
          <div className="space-y-3 text-sm">
            <div className="bg-blue-900 rounded p-3">
              <p className="font-semibold">💡 Regla del 2%</p>
              <p>Nunca arriesgues más del 2% de tu cuenta por operación</p>
            </div>
            <div className="bg-blue-900 rounded p-3">
              <p className="font-semibold">🎯 Risk/Reward 1:3</p>
              <p>Por cada $1 de riesgo, busca $3 de ganancia</p>
            </div>
            <div className="bg-blue-900 rounded p-3">
              <p className="font-semibold">📈 Diversificación</p>
              <p>No pongas todos los huevos en la misma canasta</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}



// Componente Alertas
function SeccionAlertas() {
  return (
    <>
      <h2 className="text-3xl font-bold mb-4">⚠️ Alertas de Mercado</h2>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">🚨 Alertas Activas</h3>
          <div className="space-y-3">
            <div className="bg-red-900 border border-red-500 rounded p-3">
              <p className="font-semibold text-red-300">🔴 XAUUSD - Liquidez Superior</p>
              <p className="text-sm">Precio: 2,340.50 | Objetivo: 2,345.00</p>
            </div>
            <div className="bg-yellow-900 border border-yellow-500 rounded p-3">
              <p className="font-semibold text-yellow-300">🟡 EURUSD - FVG Detectado</p>
              <p className="text-sm">Precio: 1.0850 | Zona: 1.0845-1.0855</p>
            </div>
          </div>
        </div>
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">⚙️ Configurar Alertas</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2 text-white">Par de divisas:</label>
              <select className="w-full p-3 rounded text-black font-semibold border-2 border-blue-600 focus:border-orange-500 focus:outline-none">
                <option>XAUUSD</option>
                <option>EURUSD</option>
                <option>GBPUSD</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2 text-white">Precio objetivo:</label>
              <input
                type="number"
                className="w-full p-3 rounded text-black font-semibold border-2 border-blue-600 focus:border-orange-500 focus:outline-none"
                placeholder="2340.50"
              />
            </div>
            <button className="w-full bg-orange-500 hover:bg-orange-600 text-white py-3 rounded font-semibold">
              Crear Alerta
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

// Componente TradingView
function SeccionTradingView() {
  const [parSeleccionado, setParSeleccionado] = useState('XAUUSD');

  // Función para obtener el símbolo correcto para TradingView
  const obtenerSimboloTradingView = (symbol) => {
    const simbolos = {
      'XAUUSD': 'FX_IDC:XAUUSD',
      'EURUSD': 'FX_IDC:EURUSD',
      'GBPUSD': 'FX_IDC:GBPUSD',
      'USDJPY': 'FX_IDC:USDJPY',
      'GBPJPY': 'FX_IDC:GBPJPY',
      'EURJPY': 'FX_IDC:EURJPY',
      'AUDUSD': 'FX_IDC:AUDUSD',
      'USDCAD': 'FX_IDC:USDCAD',
      'US30': 'FOREXCOM:DJI',
      'SPX500': 'FOREXCOM:SPXUSD',
      'NAS100': 'FOREXCOM:NSXUSD'
    };
    return simbolos[symbol] || `FX_IDC:${symbol}`;
  };

  const pares = [
    { symbol: 'XAUUSD', name: 'Oro/USD', category: 'Metales' },
    { symbol: 'EURUSD', name: 'Euro/USD', category: 'Forex' },
    { symbol: 'GBPUSD', name: 'Libra/USD', category: 'Forex' },
    { symbol: 'USDJPY', name: 'USD/Yen', category: 'Forex' },
    { symbol: 'GBPJPY', name: 'Libra/Yen', category: 'Forex' },
    { symbol: 'EURJPY', name: 'Euro/Yen', category: 'Forex' },
    { symbol: 'AUDUSD', name: 'AUD/USD', category: 'Forex' },
    { symbol: 'USDCAD', name: 'USD/CAD', category: 'Forex' },
    { symbol: 'US30', name: 'Dow Jones', category: 'Índices' },
    { symbol: 'SPX500', name: 'S&P 500', category: 'Índices' },
    { symbol: 'NAS100', name: 'Nasdaq', category: 'Índices' }
  ];

  return (
    <>
      <h2 className="text-3xl font-bold mb-4">📉 TradingView - Análisis en Tiempo Real</h2>

      {/* Selector de pares por categorías */}
      <div className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Forex */}
          <div className="bg-blue-800 rounded-xl p-4">
            <h4 className="text-lg font-semibold mb-3 text-center">💱 Forex</h4>
            <div className="flex flex-wrap gap-2 justify-center">
              {pares.filter(par => par.category === 'Forex').map((par) => (
                <button
                  key={par.symbol}
                  onClick={() => setParSeleccionado(par.symbol)}
                  className={`px-3 py-2 rounded-lg font-semibold transition-all text-sm ${
                    parSeleccionado === par.symbol
                      ? 'bg-orange-500 text-white'
                      : 'bg-blue-900 text-white hover:bg-blue-700'
                  }`}
                >
                  {par.symbol}
                </button>
              ))}
            </div>
          </div>

          {/* Índices */}
          <div className="bg-blue-800 rounded-xl p-4">
            <h4 className="text-lg font-semibold mb-3 text-center">📈 Índices</h4>
            <div className="flex flex-wrap gap-2 justify-center">
              {pares.filter(par => par.category === 'Índices').map((par) => (
                <button
                  key={par.symbol}
                  onClick={() => setParSeleccionado(par.symbol)}
                  className={`px-3 py-2 rounded-lg font-semibold transition-all text-sm ${
                    parSeleccionado === par.symbol
                      ? 'bg-orange-500 text-white'
                      : 'bg-blue-900 text-white hover:bg-blue-700'
                  }`}
                >
                  {par.symbol}
                </button>
              ))}
            </div>
          </div>

          {/* Metales */}
          <div className="bg-blue-800 rounded-xl p-4">
            <h4 className="text-lg font-semibold mb-3 text-center">🥇 Metales</h4>
            <div className="flex flex-wrap gap-2 justify-center">
              {pares.filter(par => par.category === 'Metales').map((par) => (
                <button
                  key={par.symbol}
                  onClick={() => setParSeleccionado(par.symbol)}
                  className={`px-3 py-2 rounded-lg font-semibold transition-all text-sm ${
                    parSeleccionado === par.symbol
                      ? 'bg-orange-500 text-white'
                      : 'bg-blue-900 text-white hover:bg-blue-700'
                  }`}
                >
                  {par.symbol}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Gráfico Principal */}
      <div className="bg-blue-800 rounded-xl p-4 mb-6">
        <h3 className="text-xl font-semibold mb-4">📊 Gráfico {parSeleccionado}</h3>
        <div className="bg-white rounded-lg overflow-hidden">
          <iframe
            src={`https://s.tradingview.com/widgetembed/?frameElementId=tradingview_chart&symbol=${obtenerSimboloTradingView(parSeleccionado)}&interval=1H&hidesidetoolbar=1&hidetoptoolbar=1&symboledit=1&saveimage=1&toolbarbg=F1F3F6&studies=[]&hideideas=1&theme=Light&style=1&timezone=Etc%2FUTC&studies_overrides={}&overrides={}&enabled_features=[]&disabled_features=[]&locale=es&utm_source=localhost&utm_medium=widget&utm_campaign=chart&utm_term=${obtenerSimboloTradingView(parSeleccionado)}`}
            width="100%"
            height="500"
            frameBorder="0"
            allowTransparency="true"
            scrolling="no"
            allowFullScreen={true}
          ></iframe>
        </div>
      </div>



      {/* Botones de acción */}
      <div className="mt-6 flex gap-4 justify-center">
        <button
          onClick={() => window.open('https://www.tradingview.com/', '_blank')}
          className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 rounded-lg font-semibold transition-all transform hover:scale-105"
        >
          🚀 Abrir TradingView Completo
        </button>
        <button
          onClick={() => window.open('https://www.tradingview.com/chart/', '_blank')}
          className="bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white px-8 py-3 rounded-lg font-semibold transition-all transform hover:scale-105"
        >
          📊 Crear Gráfico Personalizado
        </button>
      </div>
    </>
  );
}

// Componente Videos
function SeccionVideos() {
  const videos = [
    { titulo: "📚 Introducción a la Liquidez Institucional", duracion: "15:30", premium: true },
    { titulo: "🎯 Identificando Fair Value Gaps", duracion: "22:45", premium: true },
    { titulo: "💡 Estrategia Germayori Completa", duracion: "45:20", premium: true }
  ];

  return (
    <>
      <h2 className="text-3xl font-bold mb-4">📘 Videos Educativos</h2>
      <div className="bg-blue-800 rounded-xl p-6">
        <h3 className="text-xl font-semibold mb-4">🎓 Cursos GERMAYORI FVG</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {videos.map((video, index) => (
            <div key={index} className="bg-blue-900 rounded p-4">
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-semibold text-sm">{video.titulo}</h4>
                {video.premium && <span className="bg-yellow-500 text-black text-xs px-2 py-1 rounded">PREMIUM</span>}
              </div>
              <p className="text-sm text-gray-400 mb-3">Duración: {video.duracion}</p>
              <button className="w-full bg-orange-500 hover:bg-orange-600 text-white py-2 rounded text-sm font-semibold">
                Ver Video
              </button>
            </div>
          ))}
        </div>
      </div>
    </>
  );
}

// Componente Mentoría
function SeccionMentoria() {
  return (
    <>
      <h2 className="text-3xl font-bold mb-6">🎓 Mentoría Directa con GERMAYORI</h2>

      {/* Precio destacado */}
      <div className="bg-gradient-to-r from-pink-600 via-purple-600 to-indigo-600 rounded-2xl p-8 mb-8 text-center shadow-2xl border-2 border-pink-400">
        <div className="flex items-center justify-center mb-4">
          <div className="text-6xl mr-4">💎</div>
          <div>
            <h3 className="text-3xl font-bold text-white mb-2">Mentoría VIP Exclusiva</h3>
            <p className="text-pink-200 text-lg">2 Horas de Mentoría Personalizada</p>
          </div>
        </div>

        <div className="bg-white/20 backdrop-blur-sm rounded-xl p-6 mb-6">
          <div className="flex items-center justify-center mb-4">
            <span className="text-2xl text-pink-200 line-through mr-4">$150</span>
            <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-6 py-3 rounded-full">
              <span className="text-4xl font-black">$100</span>
              <span className="text-lg font-semibold ml-2">USD</span>
            </div>
          </div>
          <div className="flex items-center justify-center space-x-4 text-sm text-pink-100">
            <span className="bg-green-500/30 px-3 py-1 rounded-full">✅ Precio Especial</span>
            <span className="bg-red-500/30 px-3 py-1 rounded-full">🔥 Oferta Limitada</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <div className="text-2xl mb-2">⏰</div>
            <div className="text-white font-semibold">2 Horas</div>
            <div className="text-pink-200 text-sm">Sesión Completa</div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <div className="text-2xl mb-2">👨‍🏫</div>
            <div className="text-white font-semibold">1 a 1</div>
            <div className="text-pink-200 text-sm">Personalizada</div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <div className="text-2xl mb-2">🎯</div>
            <div className="text-white font-semibold">Estrategia</div>
            <div className="text-pink-200 text-sm">Liquidez Germayori</div>
          </div>
        </div>

        <button
          onClick={() => window.open('https://chat.whatsapp.com/L4OdlXIE4Kx3av3TSQVOS6', '_blank')}
          className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white px-12 py-4 rounded-xl font-bold text-lg transition-all transform hover:scale-105 shadow-lg"
        >
          💬 Reservar Mentoría por WhatsApp
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">📋 ¿Qué incluye la Mentoría?</h3>
          <div className="space-y-4">
            <div className="bg-blue-900 rounded-lg p-4 border-l-4 border-pink-400">
              <h4 className="font-semibold mb-2 text-pink-300">🔥 Análisis Personalizado</h4>
              <p className="text-sm text-gray-300">Revisión completa de tus operaciones y estrategias actuales</p>
            </div>
            <div className="bg-blue-900 rounded-lg p-4 border-l-4 border-purple-400">
              <h4 className="font-semibold mb-2 text-purple-300">📊 Estrategia de Liquidez</h4>
              <p className="text-sm text-gray-300">Enseñanza directa de la estrategia institucional de GERMAYORI</p>
            </div>
            <div className="bg-blue-900 rounded-lg p-4 border-l-4 border-indigo-400">
              <h4 className="font-semibold mb-2 text-indigo-300">🎯 Plan de Trading</h4>
              <p className="text-sm text-gray-300">Creación de un plan personalizado según tu perfil de riesgo</p>
            </div>
            <div className="bg-blue-900 rounded-lg p-4 border-l-4 border-green-400">
              <h4 className="font-semibold mb-2 text-green-300">💬 Seguimiento Post-Mentoría</h4>
              <p className="text-sm text-gray-300">7 días de seguimiento por WhatsApp para resolver dudas</p>
            </div>
          </div>
        </div>

        <div className="bg-blue-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">🌟 Testimonios VIP</h3>
          <div className="space-y-4">
            <div className="bg-gradient-to-r from-green-900 to-emerald-900 rounded-lg p-4 border border-green-500">
              <div className="flex items-center mb-2">
                <div className="text-2xl mr-3">👤</div>
                <div>
                  <div className="font-semibold text-green-300">Carlos M.</div>
                  <div className="text-xs text-gray-400">Trader desde 2023</div>
                </div>
              </div>
              <p className="text-sm text-gray-300">"La mentoría cambió completamente mi trading. En 2 horas aprendí más que en meses de videos."</p>
              <div className="text-yellow-400 text-sm mt-2">⭐⭐⭐⭐⭐</div>
            </div>

            <div className="bg-gradient-to-r from-blue-900 to-indigo-900 rounded-lg p-4 border border-blue-500">
              <div className="flex items-center mb-2">
                <div className="text-2xl mr-3">👤</div>
                <div>
                  <div className="font-semibold text-blue-300">María L.</div>
                  <div className="text-xs text-gray-400">Inversionista</div>
                </div>
              </div>
              <p className="text-sm text-gray-300">"GERMAYORI me enseñó a identificar la liquidez institucional. Mis resultados mejoraron 300%."</p>
              <div className="text-yellow-400 text-sm mt-2">⭐⭐⭐⭐⭐</div>
            </div>

            <div className="bg-gradient-to-r from-purple-900 to-pink-900 rounded-lg p-4 border border-purple-500">
              <div className="flex items-center mb-2">
                <div className="text-2xl mr-3">👤</div>
                <div>
                  <div className="font-semibold text-purple-300">Roberto S.</div>
                  <div className="text-xs text-gray-400">Trader Profesional</div>
                </div>
              </div>
              <p className="text-sm text-gray-300">"La mejor inversión que he hecho. La estrategia de liquidez es oro puro."</p>
              <div className="text-yellow-400 text-sm mt-2">⭐⭐⭐⭐⭐</div>
            </div>
          </div>
        </div>
      </div>

      {/* Información adicional */}
      <div className="mt-8 bg-gradient-to-r from-orange-800 to-red-800 rounded-xl p-6 border-2 border-orange-400">
        <div className="text-center">
          <h3 className="text-2xl font-bold mb-4 text-orange-200">⚠️ Información Importante</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
            <div>
              <h4 className="font-semibold text-orange-300 mb-2">📅 Disponibilidad</h4>
              <ul className="text-sm text-gray-300 space-y-1">
                <li>• Lunes a Viernes: 9:00 AM - 6:00 PM (GMT-5)</li>
                <li>• Sábados: 10:00 AM - 2:00 PM (GMT-5)</li>
                <li>• Se agenda con 24-48 horas de anticipación</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-orange-300 mb-2">💳 Métodos de Pago</h4>
              <ul className="text-sm text-gray-300 space-y-1">
                <li>• PayPal (Recomendado)</li>
                <li>• Transferencia Bancaria</li>
                <li>• Criptomonedas (USDT)</li>
                <li>• Western Union</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// Componente de Inversiones
function SeccionInversiones() {
  const [montoInversion, setMontoInversion] = useState('');
  const [mostrarCalculadora, setMostrarCalculadora] = useState(false);
  const [mostrarPago, setMostrarPago] = useState(false);
  const [datosInversion, setDatosInversion] = useState(null);
  const [cargando, setCargando] = useState(false);
  const [mensaje, setMensaje] = useState('');
  const [misInversiones, setMisInversiones] = useState([]);
  const [usuario, setUsuario] = useState(null);

  // Cargar datos del usuario y sus inversiones
  useEffect(() => {
    const usuarioGuardado = localStorage.getItem('usuario_germayori');
    if (usuarioGuardado) {
      const datosUsuario = JSON.parse(usuarioGuardado);

      setUsuario(datosUsuario);
      cargarInversiones(datosUsuario.id);
    }
  }, []);

  const cargarInversiones = async (usuarioId) => {
    try {
      const response = await fetch(`/api/inversiones?usuarioId=${usuarioId}`);
      const data = await response.json();
      if (data.success) {
        setMisInversiones(data.inversiones);
      }
    } catch (error) {
      console.error('Error cargando inversiones:', error);
    }
  };

  const calcularInversion = () => {
    const monto = parseFloat(montoInversion);
    if (monto < 100) {
      alert('El monto mínimo de inversión es $100 USD');
      return;
    }

    const rendimientoMensual = monto * 0.04; // 4% mensual
    const rendimientoTrimestral = rendimientoMensual * 3;
    const rendimientoAnual = rendimientoMensual * 12;
    const rendimientoTotal2Anos = rendimientoMensual * 24;
    const montoFinal = monto + rendimientoTotal2Anos;

    setDatosInversion({
      montoInicial: monto,
      rendimientoMensual,
      rendimientoTrimestral,
      rendimientoAnual,
      rendimientoTotal2Anos,
      montoFinal
    });

    setMostrarCalculadora(true);
  };

  const procederAlPago = async () => {
    if (!usuario) {
      setMensaje('Error: Usuario no encontrado');
      return;
    }

    setCargando(true);
    setMensaje('');

    try {
      const response = await fetch('/api/inversiones', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          usuarioId: usuario.id,
          montoInicial: datosInversion.montoInicial,
          rendimientoMensual: datosInversion.rendimientoMensual,
          rendimientoTrimestral: datosInversion.rendimientoTrimestral,
          rendimientoAnual: datosInversion.rendimientoAnual,
          rendimientoTotal2Anos: datosInversion.rendimientoTotal2Anos,
          montoFinal: datosInversion.montoFinal
        })
      });

      const data = await response.json();

      if (data.success) {
        setMensaje('¡Inversión registrada exitosamente! Procede con el pago.');
        setMostrarPago(true);
        // Recargar inversiones
        cargarInversiones(usuario.id);
      } else {
        setMensaje(data.message || 'Error al registrar la inversión');
      }
    } catch (error) {
      console.error('Error:', error);
      setMensaje('Error de conexión. Intenta de nuevo.');
    } finally {
      setCargando(false);
    }
  };

  return (
    <>
      <h2 className="text-3xl font-bold mb-6">💰 Canal de Inversiones GERMAYORI</h2>

      {/* Mensaje de estado */}
      {mensaje && (
        <div className={`mb-6 p-4 rounded-lg text-center font-semibold ${
          mensaje.includes('exitosamente')
            ? 'bg-green-100 text-green-800 border border-green-300'
            : 'bg-red-100 text-red-800 border border-red-300'
        }`}>
          {mensaje}
        </div>
      )}

      {/* Mis Inversiones Existentes */}
      {misInversiones.length > 0 && (
        <div className="bg-blue-800 rounded-xl p-6 mb-6">
          <h3 className="text-xl font-semibold mb-4">📊 Mis Inversiones Activas</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {misInversiones.map((inversion, index) => (
              <div key={index} className="bg-blue-900 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-semibold">${inversion.montoInicial.toLocaleString()}</span>
                  <span className={`px-2 py-1 rounded text-xs font-semibold ${
                    inversion.estado === 'activa' ? 'bg-green-500 text-white' :
                    inversion.estado === 'pendiente_verificacion' ? 'bg-yellow-500 text-black' :
                    'bg-gray-500 text-white'
                  }`}>
                    {inversion.estado.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
                <div className="text-sm text-gray-300 space-y-1">
                  <p>💰 Ganancia mensual: ${inversion.rendimientoMensual.toLocaleString()}</p>
                  <p>📅 Inicio: {new Date(inversion.fechaInicio).toLocaleDateString()}</p>
                  <p>⏰ Vence: {new Date(inversion.fechaVencimiento).toLocaleDateString()}</p>
                  {inversion.estado === 'activa' && (
                    <p>🎯 Próximo retiro: {new Date(inversion.proximoRetiro).toLocaleDateString()}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Información Principal */}
      <div className="bg-gradient-to-r from-green-800 to-blue-800 rounded-xl p-6 mb-6">
        <div className="text-center mb-6">
          <h3 className="text-2xl font-bold mb-2">🚀 Invierte Conmigo - 4% Mensual</h3>
          <p className="text-lg text-green-200">Rendimiento garantizado con mi estrategia de trading institucional</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-green-900 rounded-lg p-4 text-center">
            <div className="text-3xl font-bold text-green-400">4%</div>
            <div className="text-sm text-gray-300">Rendimiento Mensual</div>
          </div>
          <div className="bg-blue-900 rounded-lg p-4 text-center">
            <div className="text-3xl font-bold text-blue-400">2 Años</div>
            <div className="text-sm text-gray-300">Plazo Mínimo</div>
          </div>
          <div className="bg-purple-900 rounded-lg p-4 text-center">
            <div className="text-3xl font-bold text-purple-400">3 Meses</div>
            <div className="text-sm text-gray-300">Retiros de Ganancias</div>
          </div>
        </div>
      </div>

      {/* Condiciones */}
      <div className="bg-blue-800 rounded-xl p-6 mb-6">
        <h3 className="text-xl font-semibold mb-4">📋 Condiciones de Inversión</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold text-green-300 mb-2">✅ Beneficios</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• 4% de rendimiento mensual garantizado</li>
              <li>• Retiros de ganancias cada 3 meses</li>
              <li>• Inversión mínima: $100 USD</li>
              <li>• Respaldado por mi estrategia institucional</li>
              <li>• Transparencia total en operaciones</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold text-orange-300 mb-2">⚠️ Términos Importantes</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• Plazo mínimo: 2 años completos</li>
              <li>• Capital bloqueado durante 2 años</li>
              <li>• Solo ganancias se pueden retirar trimestralmente</li>
              <li>• Pago mediante transferencia o QR</li>
              <li>• Contrato de inversión firmado</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Calculadora */}
      <div className="bg-blue-800 rounded-xl p-6 mb-6">
        <h3 className="text-xl font-semibold mb-4">🧮 Calculadora de Inversión</h3>
        <div className="flex flex-col md:flex-row gap-4 mb-4">
          <div className="flex-1">
            <label className="block text-sm font-semibold text-gray-300 mb-2">
              Monto a Invertir (USD)
            </label>
            <input
              type="number"
              value={montoInversion}
              onChange={(e) => setMontoInversion(e.target.value)}
              className="w-full p-4 border-2 border-gray-300 rounded-lg focus:border-green-500 focus:outline-none text-lg text-gray-900 bg-white"
              placeholder="Ej: 1000"
              min="100"
            />
          </div>
          <div className="flex items-end">
            <button
              onClick={calcularInversion}
              className="bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-lg font-semibold transition-all transform hover:scale-105"
            >
              📊 Calcular Rendimiento
            </button>
          </div>
        </div>

        {mostrarCalculadora && datosInversion && (
          <div className="bg-green-900 rounded-lg p-6 mt-4">
            <h4 className="text-lg font-semibold mb-4 text-green-300">📈 Proyección de tu Inversión</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-green-800 rounded-lg p-4">
                <div className="text-sm text-gray-300">Inversión Inicial</div>
                <div className="text-2xl font-bold text-white">${datosInversion.montoInicial.toLocaleString()}</div>
              </div>
              <div className="bg-blue-800 rounded-lg p-4">
                <div className="text-sm text-gray-300">Ganancia Mensual</div>
                <div className="text-2xl font-bold text-blue-300">${datosInversion.rendimientoMensual.toLocaleString()}</div>
              </div>
              <div className="bg-purple-800 rounded-lg p-4">
                <div className="text-sm text-gray-300">Retiro Trimestral</div>
                <div className="text-2xl font-bold text-purple-300">${datosInversion.rendimientoTrimestral.toLocaleString()}</div>
              </div>
              <div className="bg-orange-800 rounded-lg p-4">
                <div className="text-sm text-gray-300">Ganancia Anual</div>
                <div className="text-2xl font-bold text-orange-300">${datosInversion.rendimientoAnual.toLocaleString()}</div>
              </div>
              <div className="bg-red-800 rounded-lg p-4">
                <div className="text-sm text-gray-300">Ganancia Total (2 años)</div>
                <div className="text-2xl font-bold text-red-300">${datosInversion.rendimientoTotal2Anos.toLocaleString()}</div>
              </div>
              <div className="bg-yellow-800 rounded-lg p-4">
                <div className="text-sm text-gray-300">Monto Final</div>
                <div className="text-2xl font-bold text-yellow-300">${datosInversion.montoFinal.toLocaleString()}</div>
              </div>
            </div>

            <div className="mt-6 text-center">
              <button
                onClick={procederAlPago}
                disabled={cargando}
                className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white px-8 py-4 rounded-lg font-bold text-lg transition-all transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {cargando ? '🔄 Registrando Inversión...' : '💰 Proceder con la Inversión'}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Sección de Pago */}
      {mostrarPago && datosInversion && (
        <div className="bg-gradient-to-r from-purple-800 to-pink-800 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4 text-center">💳 Realizar Pago de Inversión</h3>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Resumen de Inversión */}
            <div className="bg-purple-900 rounded-lg p-6">
              <h4 className="font-semibold text-purple-300 mb-4">📋 Resumen de tu Inversión</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span>Monto a Invertir:</span>
                  <span className="font-bold text-green-400">${datosInversion.montoInicial.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Rendimiento Mensual:</span>
                  <span className="font-bold text-blue-400">${datosInversion.rendimientoMensual.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Retiro Trimestral:</span>
                  <span className="font-bold text-purple-400">${datosInversion.rendimientoTrimestral.toLocaleString()}</span>
                </div>
                <div className="border-t border-purple-600 pt-3">
                  <div className="flex justify-between">
                    <span className="font-bold">Total después de 2 años:</span>
                    <span className="font-bold text-yellow-400">${datosInversion.montoFinal.toLocaleString()}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Métodos de Pago */}
            <div className="bg-pink-900 rounded-lg p-6">
              <h4 className="font-semibold text-pink-300 mb-4">💳 Métodos de Pago</h4>

              {/* QR de Pago */}
              <div className="text-center mb-6">
                <h5 className="font-semibold mb-2">📱 Código QR para Pago</h5>
                <div className="bg-white rounded-lg p-6 inline-block">
                  <Image
                    src="/qr.png"
                    alt="QR para Inversiones GERMAYORI"
                    className="w-72 h-72 mx-auto"
                  />
                </div>
                <p className="text-sm text-gray-300 mt-2">
                  Escanea el código QR para realizar el pago de tu inversión
                </p>
              </div>

              {/* Instrucciones */}
              <div className="bg-pink-800 rounded-lg p-4">
                <h5 className="font-semibold text-pink-200 mb-2">📝 Instrucciones de Pago</h5>
                <ol className="text-sm text-gray-300 space-y-1">
                  <li>1. Realiza el pago por ${datosInversion.montoInicial.toLocaleString()}</li>
                  <li>2. Toma captura de pantalla del comprobante</li>
                  <li>3. Envía el comprobante al WhatsApp VIP</li>
                  <li>4. Espera confirmación para firmar contrato</li>
                  <li>5. Tu inversión comenzará a generar rendimientos</li>
                </ol>
              </div>

              {/* Botón WhatsApp */}
              <div className="text-center mt-6">
                <button
                  onClick={() => window.open('https://chat.whatsapp.com/L4OdlXIE4Kx3av3TSQVOS6', '_blank')}
                  className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold transition-all transform hover:scale-105"
                >
                  📱 Enviar Comprobante por WhatsApp
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

// Componente de Administración de Usuarios
function SeccionAdminUsuarios() {
  const [usuarios, setUsuarios] = useState([]);
  const [cargando, setCargando] = useState(true);
  const [mensaje, setMensaje] = useState('');

  useEffect(() => {
    cargarUsuarios();
  }, []);

  const cargarUsuarios = async () => {
    try {
      const response = await fetch('/api/admin/usuarios');
      const data = await response.json();
      if (data.success) {
        setUsuarios(data.usuarios);
      } else {
        setMensaje('Error cargando usuarios');
      }
    } catch (error) {
      console.error('Error:', error);
      setMensaje('Error de conexión');
    } finally {
      setCargando(false);
    }
  };

  const actualizarEstadoUsuario = async (usuarioId, nuevoEstado) => {
    try {
      const response = await fetch('/api/admin/usuarios', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          usuarioId,
          estado: nuevoEstado
        })
      });

      const data = await response.json();
      if (data.success) {
        setMensaje(`Usuario ${nuevoEstado} exitosamente`);
        cargarUsuarios();
      } else {
        setMensaje(data.message);
      }
    } catch (error) {
      console.error('Error:', error);
      setMensaje('Error actualizando usuario');
    }
  };

  const activarUsuarioPagado = async (usuarioId, tipoActivacion = '30_dias') => {
    try {
      const usuarioAdmin = JSON.parse(localStorage.getItem('usuario_germayori'));

      const response = await fetch('/api/admin/activar-usuario', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          usuarioId,
          adminId: usuarioAdmin.id,
          tipoActivacion
        })
      });

      const data = await response.json();
      if (data.success) {
        setMensaje(`✅ Usuario activado exitosamente con acceso por ${tipoActivacion.replace('_', ' ')}`);
        cargarUsuarios();
      } else {
        setMensaje(data.message);
      }
    } catch (error) {
      console.error('Error:', error);
      setMensaje('Error activando usuario');
    }
  };

  const extenderSuscripcion = async (usuarioId, dias) => {
    try {
      const response = await fetch('/api/admin/usuarios', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          usuarioId,
          extenderDias: dias
        })
      });

      const data = await response.json();
      if (data.success) {
        setMensaje(`Suscripción extendida ${dias} días`);
        cargarUsuarios();
      } else {
        setMensaje(data.message);
      }
    } catch (error) {
      console.error('Error:', error);
      setMensaje('Error extendiendo suscripción');
    }
  };

  if (cargando) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-orange-500 mx-auto mb-4"></div>
        <p className="text-xl">Cargando usuarios...</p>
      </div>
    );
  }

  return (
    <>
      <h2 className="text-3xl font-bold mb-6">👥 Gestión de Usuarios</h2>

      {mensaje && (
        <div className={`mb-6 p-4 rounded-lg text-center font-semibold ${
          mensaje.includes('exitosamente') || mensaje.includes('extendida')
            ? 'bg-green-100 text-green-800 border border-green-300'
            : 'bg-red-100 text-red-800 border border-red-300'
        }`}>
          {mensaje}
        </div>
      )}

      <div className="bg-blue-800 rounded-xl p-6">
        <h3 className="text-xl font-semibold mb-4">📊 Lista de Usuarios</h3>

        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-blue-600">
                <th className="text-left p-3">Nombre</th>
                <th className="text-left p-3">Email</th>
                <th className="text-left p-3">Plan</th>
                <th className="text-left p-3">Estado</th>
                <th className="text-left p-3">Expira</th>
                <th className="text-left p-3">Acciones</th>
              </tr>
            </thead>
            <tbody>
              {usuarios.map((usuario, index) => (
                <tr key={index} className="border-b border-blue-700 hover:bg-blue-700">
                  <td className="p-3">{usuario.nombre}</td>
                  <td className="p-3">{usuario.correo}</td>
                  <td className="p-3">
                    <span className={`px-2 py-1 rounded text-xs ${
                      usuario.plan === 'ADMIN' ? 'bg-red-500 text-white' : 'bg-yellow-500 text-black'
                    }`}>
                      {usuario.plan}
                    </span>
                  </td>
                  <td className="p-3">
                    <span className={`px-2 py-1 rounded text-xs ${
                      usuario.estado === 'activo' ? 'bg-green-500 text-white' :
                      usuario.estado === 'inactivo' ? 'bg-red-500 text-white' :
                      'bg-yellow-500 text-black'
                    }`}>
                      {usuario.estado.toUpperCase()}
                    </span>
                  </td>
                  <td className="p-3">
                    {usuario.fechaExpiracion ? new Date(usuario.fechaExpiracion).toLocaleDateString() : 'Sin acceso'}
                  </td>
                  <td className="p-3">
                    <div className="flex flex-wrap gap-1">
                      {usuario.estado === 'inactivo' ? (
                        <>
                          <button
                            onClick={() => activarUsuarioPagado(usuario._id, '30_dias')}
                            className="bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded text-xs"
                          >
                            ✅ Activar (30d)
                          </button>
                          <button
                            onClick={() => activarUsuarioPagado(usuario._id, '60_dias')}
                            className="bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded text-xs"
                          >
                            ✅ Activar (60d)
                          </button>
                          <button
                            onClick={() => activarUsuarioPagado(usuario._id, '90_dias')}
                            className="bg-purple-500 hover:bg-purple-600 text-white px-2 py-1 rounded text-xs"
                          >
                            ✅ Activar (90d)
                          </button>
                        </>
                      ) : usuario.estado === 'activo' ? (
                        <>
                          <button
                            onClick={() => actualizarEstadoUsuario(usuario._id, 'inactivo')}
                            className="bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded text-xs"
                          >
                            ❌ Desactivar
                          </button>
                          <button
                            onClick={() => extenderSuscripcion(usuario._id, 30)}
                            className="bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded text-xs"
                          >
                            +30 días
                          </button>
                        </>
                      ) : (
                        <button
                          onClick={() => activarUsuarioPagado(usuario._id, '30_dias')}
                          className="bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded text-xs"
                        >
                          ✅ Activar
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </>
  );
}

// Componente de Administración de Inversiones
function SeccionAdminInversiones() {
  const [inversiones, setInversiones] = useState([]);
  const [cargando, setCargando] = useState(true);
  const [mensaje, setMensaje] = useState('');

  useEffect(() => {
    cargarInversiones();
  }, []);

  const cargarInversiones = async () => {
    try {
      const response = await fetch('/api/admin/inversiones');
      const data = await response.json();
      if (data.success) {
        setInversiones(data.inversiones);
      } else {
        setMensaje('Error cargando inversiones');
      }
    } catch (error) {
      console.error('Error:', error);
      setMensaje('Error de conexión');
    } finally {
      setCargando(false);
    }
  };

  const actualizarEstadoInversion = async (inversionId, nuevoEstado) => {
    try {
      const response = await fetch('/api/inversiones', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inversionId,
          estado: nuevoEstado
        })
      });

      const data = await response.json();
      if (data.success) {
        setMensaje(`Inversión ${nuevoEstado} exitosamente`);
        cargarInversiones();
      } else {
        setMensaje(data.message);
      }
    } catch (error) {
      console.error('Error:', error);
      setMensaje('Error actualizando inversión');
    }
  };

  if (cargando) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-orange-500 mx-auto mb-4"></div>
        <p className="text-xl">Cargando inversiones...</p>
      </div>
    );
  }

  return (
    <>
      <h2 className="text-3xl font-bold mb-6">💼 Gestión de Inversiones</h2>

      {mensaje && (
        <div className={`mb-6 p-4 rounded-lg text-center font-semibold ${
          mensaje.includes('exitosamente')
            ? 'bg-green-100 text-green-800 border border-green-300'
            : 'bg-red-100 text-red-800 border border-red-300'
        }`}>
          {mensaje}
        </div>
      )}

      <div className="bg-blue-800 rounded-xl p-6">
        <h3 className="text-xl font-semibold mb-4">📊 Lista de Inversiones</h3>

        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-blue-600">
                <th className="text-left p-3">Usuario</th>
                <th className="text-left p-3">Monto</th>
                <th className="text-left p-3">Rendimiento Mensual</th>
                <th className="text-left p-3">Estado</th>
                <th className="text-left p-3">Fecha Inicio</th>
                <th className="text-left p-3">Acciones</th>
              </tr>
            </thead>
            <tbody>
              {inversiones.map((inversion, index) => (
                <tr key={index} className="border-b border-blue-700 hover:bg-blue-700">
                  <td className="p-3">
                    <div>
                      <div className="font-semibold">{inversion.usuarioNombre}</div>
                      <div className="text-xs text-gray-400">{inversion.usuarioEmail}</div>
                    </div>
                  </td>
                  <td className="p-3">${inversion.montoInicial.toLocaleString()}</td>
                  <td className="p-3">${inversion.rendimientoMensual.toLocaleString()}</td>
                  <td className="p-3">
                    <span className={`px-2 py-1 rounded text-xs ${
                      inversion.estado === 'activa' ? 'bg-green-500 text-white' :
                      inversion.estado === 'pendiente_verificacion' ? 'bg-yellow-500 text-black' :
                      'bg-red-500 text-white'
                    }`}>
                      {inversion.estado.replace('_', ' ').toUpperCase()}
                    </span>
                  </td>
                  <td className="p-3">
                    {new Date(inversion.fechaInicio).toLocaleDateString()}
                  </td>
                  <td className="p-3">
                    <div className="flex flex-wrap gap-1">
                      {inversion.estado === 'pendiente_verificacion' && (
                        <button
                          onClick={() => actualizarEstadoInversion(inversion._id, 'activa')}
                          className="bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded text-xs"
                        >
                          Aprobar
                        </button>
                      )}
                      {inversion.estado === 'activa' && (
                        <button
                          onClick={() => actualizarEstadoInversion(inversion._id, 'completada')}
                          className="bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded text-xs"
                        >
                          Completar
                        </button>
                      )}
                      <button
                        onClick={() => actualizarEstadoInversion(inversion._id, 'cancelada')}
                        className="bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded text-xs"
                      >
                        Cancelar
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </>
  );
}

function Card({ title, desc, icon, tag, onClick }) {
  return (
    <div
      className="bg-blue-800 rounded-xl p-4 shadow hover:scale-105 transition-transform cursor-pointer"
      onClick={onClick}
    >
      <div className="flex items-center justify-between mb-2">
        <span className="text-2xl">{icon}</span>
        {tag && <span className="bg-pink-500 text-white text-xs px-2 py-0.5 rounded-full">{tag}</span>}
      </div>
      <h3 className="font-bold text-lg">{title}</h3>
      <p className="text-sm text-gray-200">{desc}</p>
    </div>
  );
}

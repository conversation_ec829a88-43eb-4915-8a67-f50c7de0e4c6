# 🚀 Guía de Despliegue - La Legendaria Germayori

Esta guía te ayudará a configurar tu dominio y desplegar la aplicación en producción.

## 📋 Requisitos Previos

- Node.js 18+ instalado
- Un dominio registrado (ej: `https://mi-dominio.com`)
- Acceso a un servidor web o hosting
- Base de datos MongoDB configurada

## 🔧 Configuración Automática del Dominio

### Paso 1: Ejecutar el Configurador
```bash
npm run deploy:config
```

El script te pedirá:
- Tu dominio completo (ej: `https://mi-dominio.com`)

### Paso 2: Verificar la Configuración
El script actualizará automáticamente:
- `src/config/config.js` - URLs de la aplicación
- `.env.production` - Variables de entorno de producción
- `next.config.js` - Configuración de Next.js

## 🏗️ Construcción para Producción

### Paso 3: Construir la Aplicación
```bash
npm run build:prod
```

### Paso 4: Probar Localmente (Opcional)
```bash
npm run start:prod
```

## 🌐 Despliegue en Servidor

### Opción A: Servidor con Node.js

1. **Subir archivos al servidor:**
   ```bash
   # Subir toda la carpeta del proyecto
   scp -r . usuario@tu-servidor:/ruta/a/tu/app/
   ```

2. **En el servidor, instalar dependencias:**
   ```bash
   npm install --production
   ```

3. **Iniciar la aplicación:**
   ```bash
   npm run start:prod
   ```

4. **Configurar proxy reverso (Nginx ejemplo):**
   ```nginx
   server {
       listen 80;
       server_name tu-dominio.com;
       
       location / {
           proxy_pass http://localhost:3002;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

### Opción B: Vercel (Recomendado)

1. **Instalar Vercel CLI:**
   ```bash
   npm i -g vercel
   ```

2. **Configurar variables de entorno en Vercel:**
   - Ve a tu proyecto en vercel.com
   - Settings → Environment Variables
   - Agrega todas las variables de `.env.production`

3. **Desplegar:**
   ```bash
   vercel --prod
   ```

### Opción C: Netlify

1. **Construir para exportación estática:**
   ```bash
   npm run build
   npm run export
   ```

2. **Subir la carpeta `out/` a Netlify**

## 🔒 Configuración de Seguridad

### Variables de Entorno Importantes

Asegúrate de cambiar estas variables en producción:

```env
# Cambiar por claves reales y seguras
JWT_SECRET=clave_super_secreta_para_produccion_2024
SESSION_SECRET=otra_clave_super_secreta_para_sesiones_2024

# Tu dominio real
NEXT_PUBLIC_BASE_URL=https://tu-dominio.com
NEXT_PUBLIC_API_URL=https://tu-dominio.com/api
```

### SSL/HTTPS

- Asegúrate de que tu dominio tenga certificado SSL
- Usa `https://` en todas las URLs de producción
- Configura redirects de HTTP a HTTPS

## 📊 Monitoreo y Mantenimiento

### Logs de la Aplicación
```bash
# Ver logs en tiempo real
pm2 logs legendaria-germayori

# O con node directamente
npm run start:prod > app.log 2>&1 &
```

### Base de Datos
- Hacer backups regulares de MongoDB
- Monitorear el uso de almacenamiento
- Verificar conexiones activas

## 🆘 Solución de Problemas

### Error: "Cannot connect to MongoDB"
- Verificar la cadena de conexión en `.env.production`
- Asegurar que la IP del servidor esté en la whitelist de MongoDB Atlas

### Error: "404 Not Found" en rutas
- Verificar configuración del servidor web
- Asegurar que las rutas de Next.js estén configuradas correctamente

### Error: "Environment variables not loaded"
- Verificar que el archivo `.env.production` esté en la raíz del proyecto
- Reiniciar la aplicación después de cambiar variables

## 📞 Soporte

Si tienes problemas con el despliegue:
1. Revisa los logs de la aplicación
2. Verifica la configuración de red y DNS
3. Asegúrate de que todas las dependencias estén instaladas

---

## 🎯 Checklist de Despliegue

- [ ] Ejecutar `npm run deploy:config`
- [ ] Verificar variables de entorno
- [ ] Construir con `npm run build:prod`
- [ ] Probar localmente con `npm run start:prod`
- [ ] Subir archivos al servidor
- [ ] Configurar servidor web/proxy
- [ ] Verificar SSL/HTTPS
- [ ] Probar todas las funcionalidades
- [ ] Configurar monitoreo y backups

¡Tu aplicación La Legendaria Germayori estará lista para producción! 🚀

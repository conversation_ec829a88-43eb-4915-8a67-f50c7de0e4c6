#!/bin/bash

# 🚀 Script de Despliegue Rápido para Vercel - Germayori
# Este script automatiza el despliegue completo en Vercel

echo "🚀 Desplegando Germayori en Vercel..."
echo "======================================"

# Verificar si Vercel CLI está instalado
if ! command -v vercel &> /dev/null; then
    echo "📦 Instalando Vercel CLI..."
    npm install -g vercel
fi

# Construir la aplicación
echo "🔨 Construyendo aplicación..."
npm run build

# Desplegar en Vercel
echo "🌐 Desplegando en Vercel..."
vercel --prod

echo ""
echo "✅ ¡Despliegue completado!"
echo ""
echo "📋 Próximos pasos:"
echo "1. Ve a vercel.com y configura tu dominio personalizado"
echo "2. Agrega las variables de entorno desde .env.production"
echo "3. Configura DNS para germayori.com"
echo ""
echo "🔗 Variables de entorno a configurar en Vercel:"
echo "   MONGODB_URI"
echo "   OPENAI_API_KEY"
echo "   NEXT_PUBLIC_BASE_URL=https://germayori.com"
echo "   NEXT_PUBLIC_API_URL=https://germayori.com/api"
echo "   JWT_SECRET"
echo "   SESSION_SECRET"
echo "   NODE_ENV=production"
echo ""
echo "🎉 Tu aplicación estará disponible en https://germayori.com"

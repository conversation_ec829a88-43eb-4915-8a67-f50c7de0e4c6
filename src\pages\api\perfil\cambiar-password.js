import { connectToDatabase } from '../../../lib/mongodb';
import { ObjectId } from 'mongodb';
import bcrypt from 'bcryptjs';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Método no permitido' });
  }

  try {
    const { usuarioId, passwordActual, passwordNueva } = req.body;

    // Validar datos requeridos
    if (!usuarioId || !passwordActual || !passwordNueva) {
      return res.status(400).json({
        success: false,
        message: 'Todos los campos son requeridos'
      });
    }

    // Validar nueva contraseña
    if (passwordNueva.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'La nueva contraseña debe tener al menos 6 caracteres'
      });
    }

    // Conectar a MongoDB
    const { db } = await connectToDatabase();
    const collection = db.collection('usuarios');

    // Buscar usuario
    const usuario = await collection.findOne({ _id: new ObjectId(usuarioId) });
    if (!usuario) {
      return res.status(404).json({
        success: false,
        message: 'Usuario no encontrado'
      });
    }

    // Verificar contraseña actual
    const passwordValida = await bcrypt.compare(passwordActual, usuario.password);
    if (!passwordValida) {
      return res.status(401).json({
        success: false,
        message: 'Contraseña actual incorrecta'
      });
    }

    // Encriptar nueva contraseña
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(passwordNueva, saltRounds);

    // Actualizar contraseña
    const resultado = await collection.updateOne(
      { _id: new ObjectId(usuarioId) },
      {
        $set: {
          password: hashedPassword,
          fechaActualizacion: new Date().toISOString()
        }
      }
    );

    if (resultado.modifiedCount > 0) {
      return res.status(200).json({
        success: true,
        message: 'Contraseña actualizada exitosamente'
      });
    } else {
      return res.status(500).json({
        success: false,
        message: 'Error actualizando contraseña'
      });
    }

  } catch (error) {
    console.error('❌ Error cambiando contraseña:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Error interno del servidor',
      error: error.message 
    });
  }
}

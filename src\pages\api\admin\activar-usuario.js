import { connectToDatabase } from '../../../lib/mongodb';
import { ObjectId } from 'mongodb';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Método no permitido' });
  }

  try {
    const { usuarioId, adminId, tipoActivacion = '30_dias' } = req.body;

    // Validar datos requeridos
    if (!usuarioId || !adminId) {
      return res.status(400).json({
        success: false,
        message: 'ID de usuario y administrador son requeridos'
      });
    }

    // Conectar a MongoDB
    const { db } = await connectToDatabase();
    const usuariosCollection = db.collection('usuarios');

    // Verificar que quien activa es administrador
    const admin = await usuariosCollection.findOne({ 
      _id: new ObjectId(adminId),
      rol: 'administrador'
    });

    if (!admin) {
      return res.status(403).json({
        success: false,
        message: 'Solo los administradores pueden activar usuarios'
      });
    }

    // Buscar el usuario a activar
    const usuario = await usuariosCollection.findOne({ 
      _id: new ObjectId(usuarioId) 
    });

    if (!usuario) {
      return res.status(404).json({
        success: false,
        message: 'Usuario no encontrado'
      });
    }

    // Calcular fecha de expiración según el tipo de activación
    const fechaActual = new Date();
    const fechaExpiracion = new Date();
    
    switch (tipoActivacion) {
      case '30_dias':
        fechaExpiracion.setDate(fechaActual.getDate() + 30);
        break;
      case '60_dias':
        fechaExpiracion.setDate(fechaActual.getDate() + 60);
        break;
      case '90_dias':
        fechaExpiracion.setDate(fechaActual.getDate() + 90);
        break;
      case '1_año':
        fechaExpiracion.setFullYear(fechaActual.getFullYear() + 1);
        break;
      default:
        fechaExpiracion.setDate(fechaActual.getDate() + 30);
    }

    // Actualizar usuario - ACTIVAR ACCESO
    const resultado = await usuariosCollection.updateOne(
      { _id: new ObjectId(usuarioId) },
      {
        $set: {
          estado: 'activo',
          plan: 'PREMIUM',
          pagado: true,
          fechaPago: fechaActual.toISOString(),
          fechaExpiracion: fechaExpiracion.toISOString(),
          activadoPor: admin.nombre,
          fechaActivacion: fechaActual.toISOString(),
          tipoActivacion: tipoActivacion
        }
      }
    );

    if (resultado.modifiedCount > 0) {
      // Registrar la activación en un log
      const logCollection = db.collection('logs_activaciones');
      await logCollection.insertOne({
        usuarioId: usuarioId,
        usuarioNombre: usuario.nombre,
        usuarioEmail: usuario.correo,
        adminId: adminId,
        adminNombre: admin.nombre,
        tipoActivacion: tipoActivacion,
        fechaActivacion: fechaActual.toISOString(),
        fechaExpiracion: fechaExpiracion.toISOString()
      });

      return res.status(200).json({
        success: true,
        message: `Usuario ${usuario.nombre} activado exitosamente por ${tipoActivacion}`,
        usuario: {
          id: usuario._id,
          nombre: usuario.nombre,
          correo: usuario.correo,
          estado: 'activo',
          plan: 'PREMIUM',
          fechaExpiracion: fechaExpiracion.toISOString(),
          activadoPor: admin.nombre
        }
      });
    } else {
      return res.status(500).json({
        success: false,
        message: 'No se pudo activar el usuario'
      });
    }

  } catch (error) {
    console.error('❌ Error activando usuario:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Error interno del servidor',
      error: error.message 
    });
  }
}

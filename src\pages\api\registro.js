export default async function handler(req, res) {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  console.log('🚀 REGISTRO API - Iniciando...');
  console.log('📍 Método:', req.method);
  console.log('🌍 Headers:', JSON.stringify(req.headers, null, 2));

  if (req.method === 'OPTIONS') {
    console.log('✅ OPTIONS request - respondiendo');
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    console.log('❌ Método no permitido:', req.method);
    return res.status(405).json({ success: false, message: 'Método no permitido' });
  }

  let client;

  try {
    console.log('📝 REGISTRO - Body recibido:', JSON.stringify(req.body, null, 2));
    const { nombre, email, telefono, edad, pais, password } = req.body;

    console.log('📝 REGISTRO - Datos extraídos:', {
      nombre: !!nombre,
      email: !!email,
      telefono: !!telefono,
      edad: !!edad,
      pais: !!pais,
      password: !!password,
      bodyKeys: Object.keys(req.body || {})
    });

    // Validar datos requeridos
    if (!nombre || !email || !telefono || !edad || !pais || !password) {
      console.log('❌ Faltan campos requeridos');
      return res.status(400).json({
        success: false,
        message: 'Todos los campos son requeridos'
      });
    }

    // Validar contraseña
    if (password.length < 6) {
      console.log('❌ Contraseña muy corta');
      return res.status(400).json({
        success: false,
        message: 'La contraseña debe tener al menos 6 caracteres'
      });
    }

    console.log('🔄 Intentando conectar a MongoDB...');
    console.log('🔍 Variables de entorno disponibles:', {
      MONGODB_URI: !!process.env.MONGODB_URI,
      MONGODB_DB: !!process.env.MONGODB_DB,
      NODE_ENV: process.env.NODE_ENV
    });

    // Importar MongoDB dinámicamente
    const { MongoClient } = await import('mongodb');
    const bcrypt = await import('bcryptjs');

    const uri = process.env.MONGODB_URI || "mongodb+srv://jhon:<EMAIL>/?retryWrites=true&w=majority&appName=LA-LEGENDARIA-GERMAYORI";
    console.log('🔗 URI usada:', uri.substring(0, 25) + '...');

    const client = new MongoClient(uri, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 60000,
      maxPoolSize: 10,
      retryWrites: true,
      w: 'majority'
    });

    console.log('🔗 Intentando conectar a MongoDB...');
    await client.connect();
    console.log('✅ MongoDB conectado exitosamente');

    const db = client.db(process.env.MONGODB_DB || 'legendaria-germayori');
    console.log('📊 Base de datos seleccionada:', process.env.MONGODB_DB || 'legendaria-germayori');
    const collection = db.collection('usuarios');

    // Verificar si el email ya existe
    console.log('🔍 Verificando si el email ya existe...');
    const usuarioExistente = await collection.findOne({ correo: email });
    if (usuarioExistente) {
      console.log('❌ Email ya registrado');
      await client.close();
      return res.status(400).json({ 
        success: false, 
        message: 'Este email ya está registrado' 
      });
    }

    console.log('✅ Email disponible');

    // Encriptar contraseña
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    console.log('✅ Contraseña encriptada');

    // Crear fechas
    const fechaActual = new Date();
    const fechaExpiracion = new Date();
    fechaExpiracion.setDate(fechaActual.getDate() + 30); // 30 días SOLO después del pago

    // Crear nuevo usuario - DESACTIVADO hasta activación manual
    const nuevoUsuario = {
      nombre: nombre.trim(),
      correo: email.toLowerCase().trim(),
      password: hashedPassword, // Contraseña encriptada
      celular: telefono.trim(),
      edad: parseInt(edad),
      pais: pais,
      fechaRegistro: fechaActual.toISOString(),
      plan: "PREMIUM", // Plan que tendrá cuando se active
      estado: "inactivo", // DESACTIVADO - Tú lo activarás manualmente
      fechaExpiracion: null, // Sin fecha hasta que lo actives
      fechaPagoEsperada: fechaExpiracion.toISOString(), // Cuando expirará si lo activas por 30 días
      rol: "usuario", // Rol por defecto
      pagado: false, // Indicador de pago
      fechaPago: null, // Fecha cuando se verifique el pago
      activadoPor: null, // Quién lo activó
      fechaActivacion: null // Cuándo fue activado
    };

    // Insertar en MongoDB
    console.log('💾 Insertando usuario en MongoDB...');
    const resultado = await collection.insertOne(nuevoUsuario);
    console.log('✅ Usuario insertado con ID:', resultado.insertedId);

    if (resultado.insertedId) {
      console.log('🎉 Registro exitoso');
      return res.status(201).json({
        success: true,
        message: 'Usuario registrado exitosamente',
        userId: resultado.insertedId,
        nombre: nuevoUsuario.nombre,
        email: nuevoUsuario.correo,
        fechaExpiracion: fechaExpiracion.toISOString()
      });
    } else {
      throw new Error('No se pudo insertar el usuario');
    }

  } catch (error) {
    console.error('❌ Error en registro:', error);
    console.error('❌ Stack trace:', error.stack);
    console.error('❌ Error name:', error.name);
    console.error('❌ Error code:', error.code);
    console.error('❌ Ambiente:', process.env.NODE_ENV);
    console.error('❌ Variables de entorno disponibles:', {
      MONGODB_URI: !!process.env.MONGODB_URI,
      MONGODB_DB: !!process.env.MONGODB_DB,
      NODE_ENV: process.env.NODE_ENV,
      VERCEL: !!process.env.VERCEL,
      VERCEL_URL: process.env.VERCEL_URL
    });
    
    return res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
      error: error.message,
      errorName: error.name,
      errorCode: error.code,
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString(),
      hasMongoURI: !!process.env.MONGODB_URI,
      hasMongoDB: !!process.env.MONGODB_DB
    });
  } finally {
    // Cerrar conexión
    if (client) {
      try {
        await client.close();
        console.log('🔒 Conexión MongoDB cerrada');
      } catch (closeError) {
        console.error('Error cerrando conexión:', closeError);
      }
    }
  }
}

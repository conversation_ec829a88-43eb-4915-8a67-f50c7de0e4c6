// API de prueba para verificar la conexión en producción
export default async function handler(req, res) {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    console.log('🔍 Test conexión iniciado...');
    console.log('🌍 Ambiente:', process.env.NODE_ENV);
    console.log('📅 Timestamp:', new Date().toISOString());
    
    // Verificar variables de entorno
    const envVars = {
      MONGODB_URI: !!process.env.MONGODB_URI,
      MONGODB_DB: !!process.env.MONGODB_DB,
      NODE_ENV: process.env.NODE_ENV,
      VERCEL: !!process.env.VERCEL,
      VERCEL_URL: process.env.VERCEL_URL
    };
    
    console.log('🔧 Variables de entorno:', envVars);

    // Probar conexión a MongoDB
    const { MongoClient } = await import('mongodb');
    const uri = process.env.MONGODB_URI || "mongodb+srv://jhon:<EMAIL>/?retryWrites=true&w=majority&appName=LA-LEGENDARIA-GERMAYORI";
    
    console.log('🔗 Intentando conectar con URI:', uri.substring(0, 25) + '...');
    
    const client = new MongoClient(uri, {
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 60000,
      maxPoolSize: 10,
      retryWrites: true,
      w: 'majority'
    });

    await client.connect();
    console.log('✅ MongoDB conectado exitosamente');

    const db = client.db(process.env.MONGODB_DB || 'legendaria-germayori');
    const collection = db.collection('usuarios');
    
    // Contar usuarios existentes
    const usuariosCount = await collection.countDocuments();
    console.log('👥 Total de usuarios:', usuariosCount);

    await client.close();
    console.log('🔒 Conexión cerrada');

    return res.status(200).json({
      success: true,
      message: 'Conexión exitosa',
      environment: envVars,
      usuariosRegistrados: usuariosCount,
      timestamp: new Date().toISOString(),
      server: 'germayori.com'
    });

  } catch (error) {
    console.error('❌ Error en test-conexion:', error);
    console.error('❌ Stack:', error.stack);
    
    return res.status(500).json({
      success: false,
      message: 'Error de conexión',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
  }
}

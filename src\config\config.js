// Configuración de la aplicación
const config = {
  // URLs base según el entorno
  development: {
    baseURL: 'http://localhost:3002',
    apiURL: 'http://localhost:3002/api',
    dashboardURL: 'http://localhost:3002/dashboard',
    loginURL: 'http://localhost:3002/login'
  },
  production: {
    baseURL: 'https://germayori.com',
    apiURL: 'https://germayori.com/api',
    dashboardURL: 'https://germayori.com/dashboard',
    loginURL: 'https://germayori.com/login'
  }
};

// Detectar el entorno actual
const isDevelopment = process.env.NODE_ENV === 'development' || 
                     (typeof window !== 'undefined' && window.location.hostname === 'localhost');

// Exportar la configuración actual
const currentConfig = isDevelopment ? config.development : config.production;

export default currentConfig;

// Funciones de utilidad para URLs
export const getBaseURL = () => currentConfig.baseURL;
export const getAPIURL = () => currentConfig.apiURL;
export const getDashboardURL = () => currentConfig.dashboardURL;
export const getLoginURL = () => currentConfig.loginURL;

// Función para construir URLs de API
export const buildAPIURL = (endpoint) => {
  return `${currentConfig.apiURL}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
};

// Función para construir URLs completas
export const buildURL = (path) => {
  return `${currentConfig.baseURL}${path.startsWith('/') ? path : '/' + path}`;
};

// Configuración de MongoDB (mantener segura)
export const mongoConfig = {
  connectionString: process.env.MONGODB_URI || 'mongodb+srv://jhon:<EMAIL>/?retryWrites=true&w=majority&appName=LA-LEGENDARIA-GERMAYORI'
};

// Configuración de WhatsApp
export const whatsappConfig = {
  vipGroupURL: 'https://chat.whatsapp.com/L4OdlXIE4Kx3av3TSQVOS6'
};

// Configuración de archivos
export const fileConfig = {
  maxFileSize: 5 * 1024 * 1024, // 5MB
  allowedImageTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
  uploadPath: '/uploads/perfiles/'
};

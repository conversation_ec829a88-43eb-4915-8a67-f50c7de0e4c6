import { useState, useEffect } from 'react';

// Componente Canal Manual de Señales
export default function CanalManual({ setSeccionActiva }) {
  const [senales, setSenales] = useState([]);
  const [usuario, setUsuario] = useState(null);
  const [esAdmin, setEsAdmin] = useState(false);
  const [mostrarFormulario, setMostrarFormulario] = useState(false);
  const [cargando, setCargando] = useState(true);

  // Formulario para nueva señal
  const [nuevaSenal, setNuevaSenal] = useState({
    par: '',
    tipo: '',
    entrada: '',
    sl: '',
    tp1: '',
    tp2: '',
    tp3: '',
    tp4: '',
    analisis: ''
  });

  // Verificar usuario y cargar señales
  useEffect(() => {
    const usuarioGuardado = localStorage.getItem('usuario_germayori');
    if (usuarioGuardado) {
      const datosUsuario = JSON.parse(usuarioGuardado);
      setUsuario(datosUsuario);
      
      // Verificar si es administrador autorizado para crear señales
      if (datosUsuario.correo === '<EMAIL>' || 
          datosUsuario.correo === '<EMAIL>' || 
          datosUsuario.correo === '<EMAIL>') {
        setEsAdmin(true);
      }
    }
    
    cargarSenales();
  }, []);

  // Cargar señales desde el servidor
  const cargarSenales = async () => {
    try {
      setCargando(true);

      // Intentar cargar desde el servidor con timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000); // 3 segundos timeout

      const response = await fetch('/api/senales', {
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      const data = await response.json();

      if (data.success) {
        setSenales(data.senales || []);
      } else {
        // Si no hay éxito, usar datos locales
        cargarSenalesLocales();
      }
    } catch (error) {
      console.error('Error cargando señales, usando datos locales:', error);
      cargarSenalesLocales();
    } finally {
      setCargando(false);
    }
  };

  // Cargar señales desde localStorage como respaldo
  const cargarSenalesLocales = () => {
    const senalesGuardadas = localStorage.getItem('senales_manuales');
    if (senalesGuardadas) {
      setSenales(JSON.parse(senalesGuardadas));
    } else {
      // NO CARGAR SEÑALES DE EJEMPLO - SOLO VACÍO
      setSenales([]);
    }
  };

  // Enviar nueva señal
  const enviarSenal = async (e) => {
    e.preventDefault();

    try {
      const response = await fetch('/api/senales', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...nuevaSenal,
          usuarioEmail: usuario.correo || usuario.email,
          fecha: new Date().toISOString(),
          estado: 'ACTIVA'
        })
      });

      const data = await response.json();

      if (data.success) {
        // Agregar la nueva señal al estado local
        setSenales(prev => [data.senal, ...prev]);

        // También guardar en localStorage como respaldo
        const senalesActualizadas = [data.senal, ...senales];
        localStorage.setItem('senales_manuales', JSON.stringify(senalesActualizadas));

        // Limpiar formulario
        setNuevaSenal({
          par: '',
          tipo: '',
          entrada: '',
          sl: '',
          tp1: '',
          tp2: '',
          tp3: '',
          tp4: '',
          analisis: ''
        });

        setMostrarFormulario(false);
        alert('✅ Señal enviada exitosamente');
      } else {
        alert('❌ Error enviando señal: ' + data.message);
      }
    } catch (error) {
      console.error('Error:', error);
      alert('❌ Error de conexión');
    }
  };

  // Eliminar señal
  const eliminarSenal = async (senalId) => {
    if (!confirm('¿Estás seguro de que quieres eliminar esta señal?')) {
      return;
    }

    try {
      const response = await fetch('/api/senales', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: senalId,
          usuarioEmail: usuario.correo || usuario.email
        })
      });

      const data = await response.json();

      if (data.success) {
        // Remover del estado local
        setSenales(prev => prev.filter(senal => senal.id !== senalId && senal._id !== senalId));

        // Actualizar localStorage
        const senalesActualizadas = senales.filter(senal => senal.id !== senalId && senal._id !== senalId);
        localStorage.setItem('senales_manuales', JSON.stringify(senalesActualizadas));

        alert('✅ Señal eliminada exitosamente');
      } else {
        alert('❌ Error eliminando señal: ' + data.message);
      }
    } catch (error) {
      console.error('Error:', error);
      alert('❌ Error de conexión');
    }
  };

  // Formatear fecha
  const formatearFecha = (fecha) => {
    const date = new Date(fecha);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  // Obtener color según tipo de señal
  const obtenerColorSenal = (tipo) => {
    return tipo === 'BUY' ? 'border-green-500 bg-green-900/20' : 'border-red-500 bg-red-900/20';
  };

  return (
    <div className="bg-gray-900 rounded-xl p-6 border-2 border-orange-500">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setSeccionActiva('inicio')}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
          >
            ← Inicio
          </button>
          <h2 className="text-3xl font-bold">📡 CANAL DE SEÑALES MANUAL</h2>
        </div>
        {esAdmin && (
          <button
            onClick={() => setMostrarFormulario(!mostrarFormulario)}
            className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
          >
            {mostrarFormulario ? '❌ Cancelar' : '➕ Nueva Señal'}
          </button>
        )}
      </div>

      {/* Formulario para administradores */}
      {esAdmin && mostrarFormulario && (
        <div className="bg-gray-800 rounded-lg p-6 mb-6 border border-gray-700">
          <div className="bg-yellow-600 text-black px-4 py-2 rounded-t-lg font-bold text-center mb-4">
            ENVIAR SEÑAL
          </div>
          
          <form onSubmit={enviarSenal} className="space-y-4">
            {/* Activo */}
            <div>
              <label className="block text-yellow-400 text-sm font-bold mb-2">ACTIVO</label>
              <input
                type="text"
                value={nuevaSenal.par}
                onChange={(e) => setNuevaSenal({...nuevaSenal, par: e.target.value})}
                className="w-full p-3 rounded bg-gray-700 text-white border border-gray-600 placeholder-gray-400"
                placeholder="Ej: XAUUSD, EURUSD, BTCUSD"
                required
              />
            </div>

            {/* Dirección */}
            <div>
              <label className="block text-yellow-400 text-sm font-bold mb-2">DIRECCIÓN</label>
              <select
                value={nuevaSenal.tipo}
                onChange={(e) => setNuevaSenal({...nuevaSenal, tipo: e.target.value})}
                className="w-full p-3 rounded bg-gray-700 text-white border border-gray-600"
              >
                <option value="">Seleccionar</option>
                <option value="BUY">BUY</option>
                <option value="SELL">SELL</option>
              </select>
            </div>

            {/* Entrada y Stop Loss */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-yellow-400 text-sm font-bold mb-2">ENTRADA</label>
                <input
                  type="text"
                  value={nuevaSenal.entrada}
                  onChange={(e) => setNuevaSenal({...nuevaSenal, entrada: e.target.value})}
                  className="w-full p-3 rounded bg-gray-700 text-white border border-gray-600 placeholder-gray-400"
                  placeholder="Precio"
                  required
                />
              </div>
              <div>
                <label className="block text-yellow-400 text-sm font-bold mb-2">STOP LOSS</label>
                <input
                  type="text"
                  value={nuevaSenal.sl}
                  onChange={(e) => setNuevaSenal({...nuevaSenal, sl: e.target.value})}
                  className="w-full p-3 rounded bg-red-800 text-white border border-red-600 placeholder-gray-400"
                  placeholder="SL"
                  required
                />
              </div>
            </div>

            {/* Take Profits */}
            <div>
              <label className="block text-yellow-400 text-sm font-bold mb-2">TAKE PROFITS</label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                <input
                  type="text"
                  value={nuevaSenal.tp1}
                  onChange={(e) => setNuevaSenal({...nuevaSenal, tp1: e.target.value})}
                  className="p-3 rounded bg-green-800 text-white border border-green-600 placeholder-gray-400"
                  placeholder="TP1"
                  required
                />
                <input
                  type="text"
                  value={nuevaSenal.tp2}
                  onChange={(e) => setNuevaSenal({...nuevaSenal, tp2: e.target.value})}
                  className="p-3 rounded bg-green-800 text-white border border-green-600 placeholder-gray-400"
                  placeholder="TP2"
                />
                <input
                  type="text"
                  value={nuevaSenal.tp3}
                  onChange={(e) => setNuevaSenal({...nuevaSenal, tp3: e.target.value})}
                  className="p-3 rounded bg-green-800 text-white border border-green-600 placeholder-gray-400"
                  placeholder="TP3"
                />
                <input
                  type="text"
                  value={nuevaSenal.tp4}
                  onChange={(e) => setNuevaSenal({...nuevaSenal, tp4: e.target.value})}
                  className="p-3 rounded bg-green-800 text-white border border-green-600 placeholder-gray-400"
                  placeholder="TP4"
                />
              </div>
            </div>

            {/* Análisis */}
            <div>
              <label className="block text-yellow-400 text-sm font-bold mb-2">ANÁLISIS</label>
              <textarea
                value={nuevaSenal.analisis}
                onChange={(e) => setNuevaSenal({...nuevaSenal, analisis: e.target.value})}
                className="w-full p-3 rounded bg-gray-700 text-white border border-gray-600 h-24 placeholder-gray-400"
                placeholder="Describe el análisis técnico..."
              />
            </div>

            {/* Botón enviar */}
            <button
              type="submit"
              className="w-full bg-yellow-600 hover:bg-yellow-700 text-black font-bold py-3 rounded transition-colors"
            >
              ENVIAR SEÑAL AL CANAL
            </button>
          </form>
        </div>
      )}

      {/* Lista de señales */}
      <div className="space-y-4">
        {cargando ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-orange-500 mx-auto mb-4"></div>
            <p className="text-xl">Cargando señales...</p>
          </div>
        ) : senales.length === 0 ? (
          <div className="bg-gray-800 rounded-xl p-8 text-center">
            <div className="text-6xl mb-4">📡</div>
            <h3 className="text-xl font-semibold mb-2">No hay señales disponibles</h3>
            <p className="text-gray-400">Las señales aparecerán aquí cuando sean enviadas</p>
          </div>
        ) : (
          senales.map((senal, index) => (
            <div key={senal.id || index} className={`bg-gray-800 rounded-xl p-6 border-l-4 ${obtenerColorSenal(senal.tipo)}`}>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4">
                  <div className={`px-3 py-1 rounded-full text-sm font-bold ${
                    senal.tipo === 'BUY' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
                  }`}>
                    {senal.par} {senal.tipo}
                  </div>
                  <div className="text-sm text-gray-400">
                    {formatearFecha(senal.fecha)}
                  </div>
                </div>
                {esAdmin && (
                  <button
                    onClick={() => eliminarSenal(senal.id || senal._id)}
                    className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm font-semibold transition-colors"
                  >
                    🗑️ Eliminar
                  </button>
                )}
              </div>

              <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-4">
                <div className="bg-gray-700 rounded-lg p-3">
                  <div className="text-xs text-gray-400 mb-1">Entrada</div>
                  <div className="font-bold text-white">{senal.entrada}</div>
                </div>
                <div className="bg-red-900/30 rounded-lg p-3">
                  <div className="text-xs text-red-400 mb-1">SL</div>
                  <div className="font-bold text-red-300">{senal.sl}</div>
                </div>
                <div className="bg-green-900/30 rounded-lg p-3">
                  <div className="text-xs text-green-400 mb-1">TP1</div>
                  <div className="font-bold text-green-300">{senal.tp1}</div>
                </div>
                <div className="bg-green-900/30 rounded-lg p-3">
                  <div className="text-xs text-green-400 mb-1">TP2</div>
                  <div className="font-bold text-green-300">{senal.tp2 || '-'}</div>
                </div>
                <div className="bg-green-900/30 rounded-lg p-3">
                  <div className="text-xs text-green-400 mb-1">TP3</div>
                  <div className="font-bold text-green-300">{senal.tp3 || '-'}</div>
                </div>
                <div className="bg-green-900/30 rounded-lg p-3">
                  <div className="text-xs text-green-400 mb-1">TP4</div>
                  <div className="font-bold text-green-300">{senal.tp4 || '-'}</div>
                </div>
              </div>

              {senal.analisis && (
                <div className="bg-gray-700 rounded-lg p-4">
                  <div className="text-sm font-medium text-yellow-400 mb-2">📊 Análisis:</div>
                  <div className="text-sm text-gray-300 leading-relaxed">
                    {senal.analisis}
                  </div>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
}

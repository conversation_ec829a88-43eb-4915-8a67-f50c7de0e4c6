const formidable = require("formidable");
const fs = require("fs");
const OpenAI = require("openai");

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Método no permitido" });
  }

  // Verificar API key
  if (!process.env.OPENAI_API_KEY) {
    console.error("❌ API Key de OpenAI no configurada");
    return res.status(500).json({ error: "Configuración del servidor incompleta." });
  }

  const form = new formidable.IncomingForm({
    multiples: true,
    maxFileSize: 10 * 1024 * 1024, // 10MB máximo
    keepExtensions: true
  });

  form.parse(req, async (err, fields, files) => {
    if (err) {
      console.error("❌ Error al parsear:", err);
      return res.status(500).json({ error: "Error al procesar las imágenes." });
    }

    console.log("📁 Archivos recibidos:", Object.keys(files));

    // Manejar múltiples archivos
    const fileKeys = ['diario', 'h4', 'h1', 'min30', 'min5'];
    const imageContents = [];

    for (const key of fileKeys) {
      if (files[key]) {
        try {
          const file = Array.isArray(files[key]) ? files[key][0] : files[key];
          console.log(`📷 Procesando imagen ${key}:`, file.originalFilename);

          const imageBuffer = fs.readFileSync(file.filepath);
          const base64Image = imageBuffer.toString("base64");

          imageContents.push({
            timeframe: key,
            image: base64Image
          });

          console.log(`✅ Imagen ${key} procesada correctamente`);
        } catch (fileError) {
          console.error(`❌ Error procesando imagen ${key}:`, fileError);
        }
      }
    }

    if (imageContents.length === 0) {
      console.error("❌ No se recibieron imágenes válidas");
      return res.status(400).json({ error: "No se recibieron imágenes válidas." });
    }

    console.log(`🚀 Enviando ${imageContents.length} imágenes a OpenAI...`);

    try {
      // Crear el contenido del mensaje con múltiples imágenes
      const userContent = [
        {
          type: "text",
          text: `Analiza estas ${imageContents.length} imágenes de diferentes temporalidades para dar una señal de trading usando la Estrategia de Liquidez Germayori. Las temporalidades son: ${imageContents.map(img => img.timeframe).join(', ')}.`
        }
      ];

      // Agregar cada imagen al contenido
      imageContents.forEach((imgData, index) => {
        userContent.push({
          type: "image_url",
          image_url: {
            url: `data:image/jpeg;base64,${imgData.image}`,
          },
        });
      });

      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        temperature: 0.7,
        max_tokens: 1500,
        messages: [
          {
            role: "system",
            content: `Eres GERMAYORI, experto en trading institucional. Usas la Estrategia de Liquidez Germayori (institucional refinada):

📊 ESTRATEGIA DE LIQUIDEZ GERMAYORI:

🔻 Liquidez Inferior (bajo importante):
Si el precio liquida un bajo importante, se busca una COMPRA.

🔺 Liquidez Superior (alto importante):
Si el precio liquida un alto importante, se busca una VENTA.

📉 Esperar Retroceso a Fair Value Gap (FVG):
Luego de la liquidación de liquidez, no se entra de inmediato. Se espera un retroceso claro hacia un FVG para tomar una entrada más precisa.

🏦 Confirmación con Estrategia Institucional:
Además, se analiza la acción del precio con lógica institucional (flujo de órdenes bancarias). Se considera:
- Desequilibrios
- Expansiones y retrocesos
- Impulsos alineados con la liquidez ya tomada

🚫 Evitar Order Blocks tradicionales:
No se usan bloques de órdenes simples. Pueden fallar y no son confiables por sí solos.

FORMATO DE RESPUESTA:
🟢 COMPRA o 🔴 VENTA en [PAR]
📍 Entrada: [precio]
⛔ SL: [stop loss]
🎯 TP1: [precio] | TP2: [precio] | TP3: [precio]
🧠 Justificación: [Explica usando liquidez, FVG, desequilibrios y manipulación institucional. NUNCA menciones soporte ni resistencia]`,
          },
          {
            role: "user",
            content: userContent,
          },
        ],
      });

      console.log("✅ Respuesta de OpenAI recibida correctamente");
      return res.status(200).json({ resultado: response.choices[0].message.content });

    } catch (error) {
      console.error("❌ Error al llamar a OpenAI:", error.message);

      if (error.code === 'insufficient_quota') {
        return res.status(500).json({ error: "Cuota de OpenAI agotada. Contacta al administrador." });
      }

      if (error.code === 'invalid_api_key') {
        return res.status(500).json({ error: "API Key de OpenAI inválida." });
      }

      return res.status(500).json({ error: `Error al generar la señal: ${error.message}` });
    }
  });
}

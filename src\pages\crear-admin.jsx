import { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Link from "next/link"
import Image from "next/image"

export default function CrearAdminPage() {
  const [formData, setFormData] = useState({
    nombre: '',
    email: '',
    password: '',
    claveSecreta: ''
  });
  const [loading, setLoading] = useState(false);
  const [mensaje, setMensaje] = useState('');
  const router = useRouter();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMensaje('');

    try {
      const response = await fetch('/api/crear-admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        setMensaje('¡Administrador creado exitosamente! Redirigiendo al login...');
        setTimeout(() => {
          router.push('/login');
        }, 2000);
      } else {
        setMensaje(data.message);
      }
    } catch (error) {
      console.error('Error:', error);
      setMensaje('Error de conexión. Intenta de nuevo.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Crear Administrador - GERMAYORI Academia</title>
        <meta name="description" content="Crear cuenta de administrador" />
      </Head>

      <div className="min-h-screen flex items-center justify-center px-6" style={{
        background: `
          linear-gradient(135deg, rgba(30, 58, 138, 0.9), rgba(67, 56, 202, 0.9)),
          radial-gradient(circle at 20% 20%, rgba(255, 165, 0, 0.2) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 69, 0, 0.2) 0%, transparent 50%),
          linear-gradient(45deg, #1e3a8a 0%, #3730a3 50%, #1e1b4b 100%)
        `
      }}>
        <div className="max-w-md w-full">
          {/* Logo y título */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <Image
                src="/logo.png"
                alt="GERMAYORI"
                className="w-20 h-20 rounded-full border-4 border-red-400 shadow-lg"
              />
            </div>
            <h1 className="text-4xl font-bold text-white mb-2">
              🔐 GERMAYORI ADMIN
            </h1>
            <p className="text-red-200 text-lg">
              Crear Cuenta de Administrador
            </p>
          </div>

          {/* Formulario */}
          <div className="bg-white rounded-2xl p-8 shadow-2xl">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
              Registro de Administrador
            </h2>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Nombre Completo
                </label>
                <input
                  type="text"
                  name="nombre"
                  value={formData.nombre}
                  onChange={handleChange}
                  className="w-full p-4 border-2 border-gray-300 rounded-lg focus:border-red-500 focus:outline-none text-lg text-gray-900 bg-white"
                  placeholder="Tu nombre completo"
                  required
                  disabled={loading}
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Email de Administrador
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full p-4 border-2 border-gray-300 rounded-lg focus:border-red-500 focus:outline-none text-lg text-gray-900 bg-white"
                  placeholder="<EMAIL>"
                  required
                  disabled={loading}
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Contraseña
                </label>
                <input
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  className="w-full p-4 border-2 border-gray-300 rounded-lg focus:border-red-500 focus:outline-none text-lg text-gray-900 bg-white"
                  placeholder="Contraseña segura"
                  required
                  disabled={loading}
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Clave Secreta de Administrador
                </label>
                <input
                  type="password"
                  name="claveSecreta"
                  value={formData.claveSecreta}
                  onChange={handleChange}
                  className="w-full p-4 border-2 border-red-300 rounded-lg focus:border-red-500 focus:outline-none text-lg text-gray-900 bg-red-50"
                  placeholder="Clave secreta del propietario"
                  required
                  disabled={loading}
                />
                <p className="text-sm text-red-600 mt-2">
                  La clave secreta es: <strong>GERMAYORI_ADMIN_2024</strong>
                </p>
              </div>

              <button
                type="submit"
                disabled={loading}
                className="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white py-4 rounded-lg font-bold text-lg transition-all transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? '🔄 Creando Administrador...' : '🔐 Crear Administrador'}
              </button>
            </form>

            {/* Mensaje de estado */}
            {mensaje && (
              <div className={`mt-6 p-4 rounded-lg text-center font-semibold ${mensaje.includes('exitosamente')
                  ? 'bg-green-100 text-green-800 border border-green-300'
                  : 'bg-red-100 text-red-800 border border-red-300'
                }`}>
                {mensaje}
              </div>
            )}

            {/* Enlaces adicionales */}
            <div className="mt-8 text-center space-y-4">
              <div className="border-t border-gray-200 pt-6">
                <Link
                  href="/login"
                  className="inline-block bg-gray-100 hover:bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold transition-all"
                >
                  🔙 <p>Volver al Login</p>

                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

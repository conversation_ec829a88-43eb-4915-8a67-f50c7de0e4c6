// API para manejar las señales de trading
import { MongoClient, ObjectId } from 'mongodb';

const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function connectToDatabase() {
  try {
    await client.connect();
    return client.db('germayori');
  } catch (error) {
    console.error('Error conectando a MongoDB:', error);
    throw error;
  }
}

export default async function handler(req, res) {
  try {
    const db = await connectToDatabase();
    const senalesCollection = db.collection('senales');

    if (req.method === 'GET') {
      // Obtener todas las señales ordenadas por fecha (más recientes primero)
      const senales = await senalesCollection
        .find({})
        .sort({ fecha: -1 })
        .limit(50) // Limitar a las últimas 50 señales
        .toArray();

      return res.status(200).json({
        success: true,
        senales: senales
      });

    } else if (req.method === 'POST') {
      // Crear nueva señal
      const {
        par,
        tipo,
        entrada,
        sl,
        tp1,
        tp2,
        tp3,
        tp4,
        analisis,
        usuarioId,
        fecha,
        estado
      } = req.body;

      // Validaciones básicas
      if (!par || !tipo || !entrada || !sl || !tp1 || !analisis) {
        return res.status(400).json({
          success: false,
          message: 'Faltan campos obligatorios: par, tipo, entrada, sl, tp1, analisis'
        });
      }

      // Crear la nueva señal
      const nuevaSenal = {
        par,
        tipo: tipo.toUpperCase(),
        entrada,
        sl,
        tp1,
        tp2: tp2 || null,
        tp3: tp3 || null,
        tp4: tp4 || null,
        analisis,
        usuarioId,
        fecha: fecha || new Date().toISOString(),
        estado: estado || 'ACTIVA',
        createdAt: new Date()
      };

      const resultado = await senalesCollection.insertOne(nuevaSenal);

      if (resultado.insertedId) {
        // Devolver la señal creada con su ID
        const senalCreada = {
          ...nuevaSenal,
          id: resultado.insertedId.toString()
        };

        return res.status(201).json({
          success: true,
          message: 'Señal creada exitosamente',
          senal: senalCreada
        });
      } else {
        return res.status(500).json({
          success: false,
          message: 'Error creando la señal'
        });
      }

    } else if (req.method === 'PUT') {
      // Actualizar señal existente
      const { id, ...updateData } = req.body;

      if (!id) {
        return res.status(400).json({
          success: false,
          message: 'ID de señal requerido'
        });
      }

      const resultado = await senalesCollection.updateOne(
        { _id: new ObjectId(id) },
        { 
          $set: {
            ...updateData,
            updatedAt: new Date()
          }
        }
      );

      if (resultado.modifiedCount > 0) {
        return res.status(200).json({
          success: true,
          message: 'Señal actualizada exitosamente'
        });
      } else {
        return res.status(404).json({
          success: false,
          message: 'Señal no encontrada'
        });
      }

    } else if (req.method === 'DELETE') {
      // Eliminar señal
      const { id } = req.body;

      if (!id) {
        return res.status(400).json({
          success: false,
          message: 'ID de señal requerido'
        });
      }

      const resultado = await senalesCollection.deleteOne({
        _id: new ObjectId(id)
      });

      if (resultado.deletedCount > 0) {
        return res.status(200).json({
          success: true,
          message: 'Señal eliminada exitosamente'
        });
      } else {
        return res.status(404).json({
          success: false,
          message: 'Señal no encontrada'
        });
      }

    } else {
      return res.status(405).json({
        success: false,
        message: 'Método no permitido'
      });
    }

  } catch (error) {
    console.error('Error en API de señales:', error);
    return res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
      error: error.message
    });
  }
}

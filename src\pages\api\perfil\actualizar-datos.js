import { connectToDatabase } from '../../../lib/mongodb';
import { ObjectId } from 'mongodb';

export default async function handler(req, res) {
  if (req.method !== 'PUT') {
    return res.status(405).json({ success: false, message: 'Método no permitido' });
  }

  try {
    const { usuarioId, nombre, celular, edad, pais } = req.body;

    // Validar datos requeridos
    if (!usuarioId || !nombre) {
      return res.status(400).json({
        success: false,
        message: 'Usuario ID y nombre son requeridos'
      });
    }

    // Conectar a MongoDB
    const { db } = await connectToDatabase();
    const collection = db.collection('usuarios');

    // Preparar datos para actualizar
    const datosActualizar = {
      nombre: nombre.trim(),
      fechaActualizacion: new Date().toISOString()
    };

    // Agregar campos opcionales si se proporcionan
    if (celular) datosActualizar.celular = celular.trim();
    if (edad) datosActualizar.edad = parseInt(edad);
    if (pais) datosActualizar.pais = pais;

    // Actualizar usuario
    const resultado = await collection.updateOne(
      { _id: new ObjectId(usuarioId) },
      { $set: datosActualizar }
    );

    if (resultado.modifiedCount > 0) {
      // Obtener datos actualizados
      const usuarioActualizado = await collection.findOne(
        { _id: new ObjectId(usuarioId) },
        { projection: { password: 0 } } // Excluir contraseña
      );

      return res.status(200).json({
        success: true,
        message: 'Datos actualizados exitosamente',
        usuario: usuarioActualizado
      });
    } else {
      return res.status(404).json({
        success: false,
        message: 'Usuario no encontrado o sin cambios'
      });
    }

  } catch (error) {
    console.error('❌ Error actualizando datos:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Error interno del servidor',
      error: error.message 
    });
  }
}

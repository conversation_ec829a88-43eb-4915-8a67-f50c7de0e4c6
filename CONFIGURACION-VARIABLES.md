# 🔧 Configuración de Variables de Entorno - Germayori

## ✅ Estado Actual
- ✅ MongoDB: Conectado y funcionando
- ✅ OpenAI API: Configurada para el canal de señales
- ✅ Servidor: Corriendo en puerto 3002
- ✅ Variables de entorno: Configuradas correctamente

## 📋 Variables de Entorno Configuradas

### Para Desarrollo (archivo `.env`)
```env
# Base de datos MongoDB
MONGODB_URI=mongodb+srv://jhon:<EMAIL>/?retryWrites=true&w=majority&appName=LA-LEGENDARIA-GERMAYORI

# OpenAI para análisis de señales
OPENAI_API_KEY=********************************************************************************************************************************************************************

# URLs para desarrollo
NEXT_PUBLIC_BASE_URL=http://localhost:3002
NEXT_PUBLIC_API_URL=http://localhost:3002/api

# Seguridad
JWT_SECRET=germayori_jwt_secret_2024_super_seguro
SESSION_SECRET=germayori_session_secret_2024_super_seguro

# Entorno
NODE_ENV=development
```

### Para Producción (archivo `.env.production`)
```env
# Base de datos MongoDB (igual que desarrollo)
MONGODB_URI=mongodb+srv://jhon:<EMAIL>/?retryWrites=true&w=majority&appName=LA-LEGENDARIA-GERMAYORI

# OpenAI para análisis de señales (igual que desarrollo)
OPENAI_API_KEY=********************************************************************************************************************************************************************

# URLs para producción (cambiar por tu dominio real)
NEXT_PUBLIC_BASE_URL=https://germayori.com
NEXT_PUBLIC_API_URL=https://germayori.com/api

# Seguridad (usar claves más fuertes en producción)
JWT_SECRET=clave_super_secreta_para_produccion_2024_muy_larga_y_segura
SESSION_SECRET=otra_clave_super_secreta_para_sesiones_produccion_2024

# Entorno
NODE_ENV=production
PORT=3002
```

## 🚀 Cómo Subir a Producción

### Opción 1: Servidor Propio
1. **Subir archivos al servidor**
2. **Instalar dependencias:**
   ```bash
   npm install
   ```
3. **Construir la aplicación:**
   ```bash
   npm run build:prod
   ```
4. **Iniciar en producción:**
   ```bash
   npm run start:prod
   ```

### Opción 2: Vercel (Recomendado)
1. **Conectar tu repositorio a Vercel**
2. **Configurar variables de entorno en Vercel:**
   - Ve a tu proyecto en vercel.com
   - Settings → Environment Variables
   - Agrega todas las variables de `.env.production`
3. **Desplegar automáticamente**

### Opción 3: Netlify
1. **Construir para exportación:**
   ```bash
   npm run build
   ```
2. **Subir la carpeta `out/` a Netlify**

## 🔒 Seguridad Importante

### ⚠️ NUNCA subas el archivo `.env` a GitHub
- El archivo `.env` está en `.gitignore`
- Solo sube `.env.example` como referencia

### 🔑 Cambiar claves en producción
- Genera claves JWT y SESSION más fuertes
- Usa herramientas como: `openssl rand -base64 32`

## 🧪 Probar la Configuración

### 1. Probar conexión a MongoDB:
```bash
node test-mongo.js
```

### 2. Probar API de test:
Visita: `http://localhost:3002/api/test-db`

### 3. Probar registro de usuarios:
Visita: `http://localhost:3002` y usa el formulario de registro

## 📞 Soporte
Si tienes problemas:
1. Verifica que todas las variables estén configuradas
2. Reinicia el servidor después de cambiar variables
3. Revisa los logs en la consola del navegador y terminal
